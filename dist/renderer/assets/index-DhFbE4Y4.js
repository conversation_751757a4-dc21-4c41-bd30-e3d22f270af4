var $v=Object.defineProperty;var Uv=(e,t,n)=>t in e?$v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ut=(e,t,n)=>Uv(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function tp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var np={exports:{}},Jo={},rp={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hi=Symbol.for("react.element"),Bv=Symbol.for("react.portal"),Hv=Symbol.for("react.fragment"),Wv=Symbol.for("react.strict_mode"),Kv=Symbol.for("react.profiler"),Gv=Symbol.for("react.provider"),qv=Symbol.for("react.context"),Qv=Symbol.for("react.forward_ref"),Xv=Symbol.for("react.suspense"),Yv=Symbol.for("react.memo"),Zv=Symbol.for("react.lazy"),Qd=Symbol.iterator;function Jv(e){return e===null||typeof e!="object"?null:(e=Qd&&e[Qd]||e["@@iterator"],typeof e=="function"?e:null)}var sp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ip=Object.assign,op={};function Jr(e,t,n){this.props=e,this.context=t,this.refs=op,this.updater=n||sp}Jr.prototype.isReactComponent={};Jr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ap(){}ap.prototype=Jr.prototype;function Wu(e,t,n){this.props=e,this.context=t,this.refs=op,this.updater=n||sp}var Ku=Wu.prototype=new ap;Ku.constructor=Wu;ip(Ku,Jr.prototype);Ku.isPureReactComponent=!0;var Xd=Array.isArray,lp=Object.prototype.hasOwnProperty,Gu={current:null},up={key:!0,ref:!0,__self:!0,__source:!0};function cp(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)lp.call(t,r)&&!up.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:hi,type:e,key:i,ref:o,props:s,_owner:Gu.current}}function ex(e,t){return{$$typeof:hi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function qu(e){return typeof e=="object"&&e!==null&&e.$$typeof===hi}function tx(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Yd=/\/+/g;function Da(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tx(""+e.key):t.toString(36)}function Qi(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case hi:case Bv:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Da(o,0):r,Xd(s)?(n="",e!=null&&(n=e.replace(Yd,"$&/")+"/"),Qi(s,t,n,"",function(c){return c})):s!=null&&(qu(s)&&(s=ex(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Yd,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Xd(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+Da(i,a);o+=Qi(i,t,n,u,s)}else if(u=Jv(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+Da(i,a++),o+=Qi(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Ti(e,t,n){if(e==null)return e;var r=[],s=0;return Qi(e,r,"","",function(i){return t.call(n,i,s++)}),r}function nx(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ke={current:null},Xi={transition:null},rx={ReactCurrentDispatcher:Ke,ReactCurrentBatchConfig:Xi,ReactCurrentOwner:Gu};function dp(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:Ti,forEach:function(e,t,n){Ti(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ti(e,function(){t++}),t},toArray:function(e){return Ti(e,function(t){return t})||[]},only:function(e){if(!qu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=Jr;J.Fragment=Hv;J.Profiler=Kv;J.PureComponent=Wu;J.StrictMode=Wv;J.Suspense=Xv;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rx;J.act=dp;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ip({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Gu.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)lp.call(t,u)&&!up.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:hi,type:e.type,key:s,ref:i,props:r,_owner:o}};J.createContext=function(e){return e={$$typeof:qv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gv,_context:e},e.Consumer=e};J.createElement=cp;J.createFactory=function(e){var t=cp.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:Qv,render:e}};J.isValidElement=qu;J.lazy=function(e){return{$$typeof:Zv,_payload:{_status:-1,_result:e},_init:nx}};J.memo=function(e,t){return{$$typeof:Yv,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Xi.transition;Xi.transition={};try{e()}finally{Xi.transition=t}};J.unstable_act=dp;J.useCallback=function(e,t){return Ke.current.useCallback(e,t)};J.useContext=function(e){return Ke.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Ke.current.useDeferredValue(e)};J.useEffect=function(e,t){return Ke.current.useEffect(e,t)};J.useId=function(){return Ke.current.useId()};J.useImperativeHandle=function(e,t,n){return Ke.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Ke.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Ke.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Ke.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Ke.current.useReducer(e,t,n)};J.useRef=function(e){return Ke.current.useRef(e)};J.useState=function(e){return Ke.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Ke.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Ke.current.useTransition()};J.version="18.3.1";rp.exports=J;var w=rp.exports;const Z=tp(w);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sx=w,ix=Symbol.for("react.element"),ox=Symbol.for("react.fragment"),ax=Object.prototype.hasOwnProperty,lx=sx.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ux={key:!0,ref:!0,__self:!0,__source:!0};function fp(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)ax.call(t,r)&&!ux.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:ix,type:e,key:i,ref:o,props:s,_owner:lx.current}}Jo.Fragment=ox;Jo.jsx=fp;Jo.jsxs=fp;np.exports=Jo;var l=np.exports,Cl={},hp={exports:{}},ut={},mp={exports:{}},pp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,W){var X=L.length;L.push(W);e:for(;0<X;){var ce=X-1>>>1,ke=L[ce];if(0<s(ke,W))L[ce]=W,L[X]=ke,X=ce;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var W=L[0],X=L.pop();if(X!==W){L[0]=X;e:for(var ce=0,ke=L.length,Fn=ke>>>1;ce<Fn;){var zt=2*(ce+1)-1,mr=L[zt],$t=zt+1,an=L[$t];if(0>s(mr,X))$t<ke&&0>s(an,mr)?(L[ce]=an,L[$t]=X,ce=$t):(L[ce]=mr,L[zt]=X,ce=zt);else if($t<ke&&0>s(an,X))L[ce]=an,L[$t]=X,ce=$t;else break e}}return W}function s(L,W){var X=L.sortIndex-W.sortIndex;return X!==0?X:L.id-W.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],c=[],d=1,h=null,f=3,g=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(L){for(var W=n(c);W!==null;){if(W.callback===null)r(c);else if(W.startTime<=L)r(c),W.sortIndex=W.expirationTime,t(u,W);else break;W=n(c)}}function k(L){if(x=!1,y(L),!v)if(n(u)!==null)v=!0,H(j);else{var W=n(c);W!==null&&U(k,W.startTime-L)}}function j(L,W){v=!1,x&&(x=!1,p(T),T=-1),g=!0;var X=f;try{for(y(W),h=n(u);h!==null&&(!(h.expirationTime>W)||L&&!R());){var ce=h.callback;if(typeof ce=="function"){h.callback=null,f=h.priorityLevel;var ke=ce(h.expirationTime<=W);W=e.unstable_now(),typeof ke=="function"?h.callback=ke:h===n(u)&&r(u),y(W)}else r(u);h=n(u)}if(h!==null)var Fn=!0;else{var zt=n(c);zt!==null&&U(k,zt.startTime-W),Fn=!1}return Fn}finally{h=null,f=X,g=!1}}var C=!1,_=null,T=-1,E=5,A=-1;function R(){return!(e.unstable_now()-A<E)}function D(){if(_!==null){var L=e.unstable_now();A=L;var W=!0;try{W=_(!0,L)}finally{W?F():(C=!1,_=null)}}else C=!1}var F;if(typeof m=="function")F=function(){m(D)};else if(typeof MessageChannel<"u"){var Y=new MessageChannel,G=Y.port2;Y.port1.onmessage=D,F=function(){G.postMessage(null)}}else F=function(){S(D,0)};function H(L){_=L,C||(C=!0,F())}function U(L,W){T=S(function(){L(e.unstable_now())},W)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,H(j))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(L){switch(f){case 1:case 2:case 3:var W=3;break;default:W=f}var X=f;f=W;try{return L()}finally{f=X}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,W){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var X=f;f=L;try{return W()}finally{f=X}},e.unstable_scheduleCallback=function(L,W,X){var ce=e.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?ce+X:ce):X=ce,L){case 1:var ke=-1;break;case 2:ke=250;break;case 5:ke=**********;break;case 4:ke=1e4;break;default:ke=5e3}return ke=X+ke,L={id:d++,callback:W,priorityLevel:L,startTime:X,expirationTime:ke,sortIndex:-1},X>ce?(L.sortIndex=X,t(c,L),n(u)===null&&L===n(c)&&(x?(p(T),T=-1):x=!0,U(k,X-ce))):(L.sortIndex=ke,t(u,L),v||g||(v=!0,H(j))),L},e.unstable_shouldYield=R,e.unstable_wrapCallback=function(L){var W=f;return function(){var X=f;f=W;try{return L.apply(this,arguments)}finally{f=X}}}})(pp);mp.exports=pp;var cx=mp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dx=w,it=cx;function V(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var gp=new Set,zs={};function cr(e,t){zr(e,t),zr(e+"Capture",t)}function zr(e,t){for(zs[e]=t,e=0;e<t.length;e++)gp.add(t[e])}var en=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Nl=Object.prototype.hasOwnProperty,fx=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Zd={},Jd={};function hx(e){return Nl.call(Jd,e)?!0:Nl.call(Zd,e)?!1:fx.test(e)?Jd[e]=!0:(Zd[e]=!0,!1)}function mx(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function px(e,t,n,r){if(t===null||typeof t>"u"||mx(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ge(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new Ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new Ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new Ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new Ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new Ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new Ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new Ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new Ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new Ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var Qu=/[\-:]([a-z])/g;function Xu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Qu,Xu);Re[t]=new Ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Qu,Xu);Re[t]=new Ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Qu,Xu);Re[t]=new Ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new Ge(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new Ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new Ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function Yu(e,t,n,r){var s=Re.hasOwnProperty(t)?Re[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(px(t,n,s,r)&&(n=null),r||s===null?hx(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var on=dx.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ji=Symbol.for("react.element"),yr=Symbol.for("react.portal"),vr=Symbol.for("react.fragment"),Zu=Symbol.for("react.strict_mode"),_l=Symbol.for("react.profiler"),yp=Symbol.for("react.provider"),vp=Symbol.for("react.context"),Ju=Symbol.for("react.forward_ref"),El=Symbol.for("react.suspense"),Pl=Symbol.for("react.suspense_list"),ec=Symbol.for("react.memo"),fn=Symbol.for("react.lazy"),xp=Symbol.for("react.offscreen"),ef=Symbol.iterator;function is(e){return e===null||typeof e!="object"?null:(e=ef&&e[ef]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Object.assign,Ma;function ys(e){if(Ma===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ma=t&&t[1]||""}return`
`+Ma+e}var Ra=!1;function La(e,t){if(!e||Ra)return"";Ra=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{Ra=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ys(e):""}function gx(e){switch(e.tag){case 5:return ys(e.type);case 16:return ys("Lazy");case 13:return ys("Suspense");case 19:return ys("SuspenseList");case 0:case 2:case 15:return e=La(e.type,!1),e;case 11:return e=La(e.type.render,!1),e;case 1:return e=La(e.type,!0),e;default:return""}}function Al(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case vr:return"Fragment";case yr:return"Portal";case _l:return"Profiler";case Zu:return"StrictMode";case El:return"Suspense";case Pl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case vp:return(e.displayName||"Context")+".Consumer";case yp:return(e._context.displayName||"Context")+".Provider";case Ju:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ec:return t=e.displayName||null,t!==null?t:Al(e.type)||"Memo";case fn:t=e._payload,e=e._init;try{return Al(e(t))}catch{}}return null}function yx(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Al(t);case 8:return t===Zu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Cn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function wp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vx(e){var t=wp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ci(e){e._valueTracker||(e._valueTracker=vx(e))}function Sp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=wp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ho(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Dl(e,t){var n=t.checked;return ge({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Cn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function bp(e,t){t=t.checked,t!=null&&Yu(e,"checked",t,!1)}function Ml(e,t){bp(e,t);var n=Cn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Rl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Rl(e,t.type,Cn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Rl(e,t,n){(t!=="number"||ho(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var vs=Array.isArray;function Rr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Cn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Ll(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(V(91));return ge({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(V(92));if(vs(n)){if(1<n.length)throw Error(V(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Cn(n)}}function kp(e,t){var n=Cn(t.value),r=Cn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function sf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Tp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Tp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ni,jp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ni=Ni||document.createElement("div"),Ni.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ni.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $s(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var js={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xx=["Webkit","ms","Moz","O"];Object.keys(js).forEach(function(e){xx.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),js[t]=js[e]})});function Cp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||js.hasOwnProperty(e)&&js[e]?(""+t).trim():t+"px"}function Np(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Cp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var wx=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Fl(e,t){if(t){if(wx[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(V(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(V(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(V(61))}if(t.style!=null&&typeof t.style!="object")throw Error(V(62))}}function Il(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ol=null;function tc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var zl=null,Lr=null,Vr=null;function of(e){if(e=gi(e)){if(typeof zl!="function")throw Error(V(280));var t=e.stateNode;t&&(t=sa(t),zl(e.stateNode,e.type,t))}}function _p(e){Lr?Vr?Vr.push(e):Vr=[e]:Lr=e}function Ep(){if(Lr){var e=Lr,t=Vr;if(Vr=Lr=null,of(e),t)for(e=0;e<t.length;e++)of(t[e])}}function Pp(e,t){return e(t)}function Ap(){}var Va=!1;function Dp(e,t,n){if(Va)return e(t,n);Va=!0;try{return Pp(e,t,n)}finally{Va=!1,(Lr!==null||Vr!==null)&&(Ap(),Ep())}}function Us(e,t){var n=e.stateNode;if(n===null)return null;var r=sa(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(V(231,t,typeof n));return n}var $l=!1;if(en)try{var os={};Object.defineProperty(os,"passive",{get:function(){$l=!0}}),window.addEventListener("test",os,os),window.removeEventListener("test",os,os)}catch{$l=!1}function Sx(e,t,n,r,s,i,o,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var Cs=!1,mo=null,po=!1,Ul=null,bx={onError:function(e){Cs=!0,mo=e}};function kx(e,t,n,r,s,i,o,a,u){Cs=!1,mo=null,Sx.apply(bx,arguments)}function Tx(e,t,n,r,s,i,o,a,u){if(kx.apply(this,arguments),Cs){if(Cs){var c=mo;Cs=!1,mo=null}else throw Error(V(198));po||(po=!0,Ul=c)}}function dr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Mp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function af(e){if(dr(e)!==e)throw Error(V(188))}function jx(e){var t=e.alternate;if(!t){if(t=dr(e),t===null)throw Error(V(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return af(s),e;if(i===r)return af(s),t;i=i.sibling}throw Error(V(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(V(189))}}if(n.alternate!==r)throw Error(V(190))}if(n.tag!==3)throw Error(V(188));return n.stateNode.current===n?e:t}function Rp(e){return e=jx(e),e!==null?Lp(e):null}function Lp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Lp(e);if(t!==null)return t;e=e.sibling}return null}var Vp=it.unstable_scheduleCallback,lf=it.unstable_cancelCallback,Cx=it.unstable_shouldYield,Nx=it.unstable_requestPaint,Se=it.unstable_now,_x=it.unstable_getCurrentPriorityLevel,nc=it.unstable_ImmediatePriority,Fp=it.unstable_UserBlockingPriority,go=it.unstable_NormalPriority,Ex=it.unstable_LowPriority,Ip=it.unstable_IdlePriority,ea=null,Vt=null;function Px(e){if(Vt&&typeof Vt.onCommitFiberRoot=="function")try{Vt.onCommitFiberRoot(ea,e,void 0,(e.current.flags&128)===128)}catch{}}var Et=Math.clz32?Math.clz32:Mx,Ax=Math.log,Dx=Math.LN2;function Mx(e){return e>>>=0,e===0?32:31-(Ax(e)/Dx|0)|0}var _i=64,Ei=4194304;function xs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function yo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=xs(a):(i&=o,i!==0&&(r=xs(i)))}else o=n&~s,o!==0?r=xs(o):i!==0&&(r=xs(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Et(t),s=1<<n,r|=e[n],t&=~s;return r}function Rx(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lx(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Et(i),a=1<<o,u=s[o];u===-1?(!(a&n)||a&r)&&(s[o]=Rx(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function Bl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Op(){var e=_i;return _i<<=1,!(_i&4194240)&&(_i=64),e}function Fa(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function mi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Et(t),e[t]=n}function Vx(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Et(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function rc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Et(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var ne=0;function zp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var $p,sc,Up,Bp,Hp,Hl=!1,Pi=[],vn=null,xn=null,wn=null,Bs=new Map,Hs=new Map,mn=[],Fx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function uf(e,t){switch(e){case"focusin":case"focusout":vn=null;break;case"dragenter":case"dragleave":xn=null;break;case"mouseover":case"mouseout":wn=null;break;case"pointerover":case"pointerout":Bs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hs.delete(t.pointerId)}}function as(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=gi(t),t!==null&&sc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Ix(e,t,n,r,s){switch(t){case"focusin":return vn=as(vn,e,t,n,r,s),!0;case"dragenter":return xn=as(xn,e,t,n,r,s),!0;case"mouseover":return wn=as(wn,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Bs.set(i,as(Bs.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Hs.set(i,as(Hs.get(i)||null,e,t,n,r,s)),!0}return!1}function Wp(e){var t=Gn(e.target);if(t!==null){var n=dr(t);if(n!==null){if(t=n.tag,t===13){if(t=Mp(n),t!==null){e.blockedOn=t,Hp(e.priority,function(){Up(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ol=r,n.target.dispatchEvent(r),Ol=null}else return t=gi(n),t!==null&&sc(t),e.blockedOn=n,!1;t.shift()}return!0}function cf(e,t,n){Yi(e)&&n.delete(t)}function Ox(){Hl=!1,vn!==null&&Yi(vn)&&(vn=null),xn!==null&&Yi(xn)&&(xn=null),wn!==null&&Yi(wn)&&(wn=null),Bs.forEach(cf),Hs.forEach(cf)}function ls(e,t){e.blockedOn===t&&(e.blockedOn=null,Hl||(Hl=!0,it.unstable_scheduleCallback(it.unstable_NormalPriority,Ox)))}function Ws(e){function t(s){return ls(s,e)}if(0<Pi.length){ls(Pi[0],e);for(var n=1;n<Pi.length;n++){var r=Pi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(vn!==null&&ls(vn,e),xn!==null&&ls(xn,e),wn!==null&&ls(wn,e),Bs.forEach(t),Hs.forEach(t),n=0;n<mn.length;n++)r=mn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<mn.length&&(n=mn[0],n.blockedOn===null);)Wp(n),n.blockedOn===null&&mn.shift()}var Fr=on.ReactCurrentBatchConfig,vo=!0;function zx(e,t,n,r){var s=ne,i=Fr.transition;Fr.transition=null;try{ne=1,ic(e,t,n,r)}finally{ne=s,Fr.transition=i}}function $x(e,t,n,r){var s=ne,i=Fr.transition;Fr.transition=null;try{ne=4,ic(e,t,n,r)}finally{ne=s,Fr.transition=i}}function ic(e,t,n,r){if(vo){var s=Wl(e,t,n,r);if(s===null)Ga(e,t,r,xo,n),uf(e,r);else if(Ix(s,e,t,n,r))r.stopPropagation();else if(uf(e,r),t&4&&-1<Fx.indexOf(e)){for(;s!==null;){var i=gi(s);if(i!==null&&$p(i),i=Wl(e,t,n,r),i===null&&Ga(e,t,r,xo,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Ga(e,t,r,null,n)}}var xo=null;function Wl(e,t,n,r){if(xo=null,e=tc(r),e=Gn(e),e!==null)if(t=dr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Mp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xo=e,null}function Kp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(_x()){case nc:return 1;case Fp:return 4;case go:case Ex:return 16;case Ip:return 536870912;default:return 16}default:return 16}}var gn=null,oc=null,Zi=null;function Gp(){if(Zi)return Zi;var e,t=oc,n=t.length,r,s="value"in gn?gn.value:gn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Zi=s.slice(e,1<r?1-r:void 0)}function Ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ai(){return!0}function df(){return!1}function ct(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ai:df,this.isPropagationStopped=df,this}return ge(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ai)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ai)},persist:function(){},isPersistent:Ai}),t}var es={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ac=ct(es),pi=ge({},es,{view:0,detail:0}),Ux=ct(pi),Ia,Oa,us,ta=ge({},pi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:lc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==us&&(us&&e.type==="mousemove"?(Ia=e.screenX-us.screenX,Oa=e.screenY-us.screenY):Oa=Ia=0,us=e),Ia)},movementY:function(e){return"movementY"in e?e.movementY:Oa}}),ff=ct(ta),Bx=ge({},ta,{dataTransfer:0}),Hx=ct(Bx),Wx=ge({},pi,{relatedTarget:0}),za=ct(Wx),Kx=ge({},es,{animationName:0,elapsedTime:0,pseudoElement:0}),Gx=ct(Kx),qx=ge({},es,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qx=ct(qx),Xx=ge({},es,{data:0}),hf=ct(Xx),Yx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function e1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jx[e])?!!t[e]:!1}function lc(){return e1}var t1=ge({},pi,{key:function(e){if(e.key){var t=Yx[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:lc,charCode:function(e){return e.type==="keypress"?Ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),n1=ct(t1),r1=ge({},ta,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),mf=ct(r1),s1=ge({},pi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:lc}),i1=ct(s1),o1=ge({},es,{propertyName:0,elapsedTime:0,pseudoElement:0}),a1=ct(o1),l1=ge({},ta,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),u1=ct(l1),c1=[9,13,27,32],uc=en&&"CompositionEvent"in window,Ns=null;en&&"documentMode"in document&&(Ns=document.documentMode);var d1=en&&"TextEvent"in window&&!Ns,qp=en&&(!uc||Ns&&8<Ns&&11>=Ns),pf=" ",gf=!1;function Qp(e,t){switch(e){case"keyup":return c1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xr=!1;function f1(e,t){switch(e){case"compositionend":return Xp(t);case"keypress":return t.which!==32?null:(gf=!0,pf);case"textInput":return e=t.data,e===pf&&gf?null:e;default:return null}}function h1(e,t){if(xr)return e==="compositionend"||!uc&&Qp(e,t)?(e=Gp(),Zi=oc=gn=null,xr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qp&&t.locale!=="ko"?null:t.data;default:return null}}var m1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!m1[e.type]:t==="textarea"}function Yp(e,t,n,r){_p(r),t=wo(t,"onChange"),0<t.length&&(n=new ac("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var _s=null,Ks=null;function p1(e){lg(e,0)}function na(e){var t=br(e);if(Sp(t))return e}function g1(e,t){if(e==="change")return t}var Zp=!1;if(en){var $a;if(en){var Ua="oninput"in document;if(!Ua){var vf=document.createElement("div");vf.setAttribute("oninput","return;"),Ua=typeof vf.oninput=="function"}$a=Ua}else $a=!1;Zp=$a&&(!document.documentMode||9<document.documentMode)}function xf(){_s&&(_s.detachEvent("onpropertychange",Jp),Ks=_s=null)}function Jp(e){if(e.propertyName==="value"&&na(Ks)){var t=[];Yp(t,Ks,e,tc(e)),Dp(p1,t)}}function y1(e,t,n){e==="focusin"?(xf(),_s=t,Ks=n,_s.attachEvent("onpropertychange",Jp)):e==="focusout"&&xf()}function v1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return na(Ks)}function x1(e,t){if(e==="click")return na(t)}function w1(e,t){if(e==="input"||e==="change")return na(t)}function S1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var At=typeof Object.is=="function"?Object.is:S1;function Gs(e,t){if(At(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Nl.call(t,s)||!At(e[s],t[s]))return!1}return!0}function wf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sf(e,t){var n=wf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wf(n)}}function eg(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?eg(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function tg(){for(var e=window,t=ho();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ho(e.document)}return t}function cc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function b1(e){var t=tg(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&eg(n.ownerDocument.documentElement,n)){if(r!==null&&cc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Sf(n,i);var o=Sf(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var k1=en&&"documentMode"in document&&11>=document.documentMode,wr=null,Kl=null,Es=null,Gl=!1;function bf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Gl||wr==null||wr!==ho(r)||(r=wr,"selectionStart"in r&&cc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Es&&Gs(Es,r)||(Es=r,r=wo(Kl,"onSelect"),0<r.length&&(t=new ac("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=wr)))}function Di(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:Di("Animation","AnimationEnd"),animationiteration:Di("Animation","AnimationIteration"),animationstart:Di("Animation","AnimationStart"),transitionend:Di("Transition","TransitionEnd")},Ba={},ng={};en&&(ng=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);function ra(e){if(Ba[e])return Ba[e];if(!Sr[e])return e;var t=Sr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ng)return Ba[e]=t[n];return e}var rg=ra("animationend"),sg=ra("animationiteration"),ig=ra("animationstart"),og=ra("transitionend"),ag=new Map,kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function An(e,t){ag.set(e,t),cr(t,[e])}for(var Ha=0;Ha<kf.length;Ha++){var Wa=kf[Ha],T1=Wa.toLowerCase(),j1=Wa[0].toUpperCase()+Wa.slice(1);An(T1,"on"+j1)}An(rg,"onAnimationEnd");An(sg,"onAnimationIteration");An(ig,"onAnimationStart");An("dblclick","onDoubleClick");An("focusin","onFocus");An("focusout","onBlur");An(og,"onTransitionEnd");zr("onMouseEnter",["mouseout","mouseover"]);zr("onMouseLeave",["mouseout","mouseover"]);zr("onPointerEnter",["pointerout","pointerover"]);zr("onPointerLeave",["pointerout","pointerover"]);cr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));cr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));cr("onBeforeInput",["compositionend","keypress","textInput","paste"]);cr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ws="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),C1=new Set("cancel close invalid load scroll toggle".split(" ").concat(ws));function Tf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Tx(r,t,void 0,e),e.currentTarget=null}function lg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;Tf(s,a,c),i=u}else for(o=0;o<r.length;o++){if(a=r[o],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;Tf(s,a,c),i=u}}}if(po)throw e=Ul,po=!1,Ul=null,e}function oe(e,t){var n=t[Zl];n===void 0&&(n=t[Zl]=new Set);var r=e+"__bubble";n.has(r)||(ug(t,e,2,!1),n.add(r))}function Ka(e,t,n){var r=0;t&&(r|=4),ug(n,e,r,t)}var Mi="_reactListening"+Math.random().toString(36).slice(2);function qs(e){if(!e[Mi]){e[Mi]=!0,gp.forEach(function(n){n!=="selectionchange"&&(C1.has(n)||Ka(n,!1,e),Ka(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mi]||(t[Mi]=!0,Ka("selectionchange",!1,t))}}function ug(e,t,n,r){switch(Kp(t)){case 1:var s=zx;break;case 4:s=$x;break;default:s=ic}n=s.bind(null,t,n,e),s=void 0,!$l||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Ga(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;a!==null;){if(o=Gn(a),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}a=a.parentNode}}r=r.return}Dp(function(){var c=i,d=tc(n),h=[];e:{var f=ag.get(e);if(f!==void 0){var g=ac,v=e;switch(e){case"keypress":if(Ji(n)===0)break e;case"keydown":case"keyup":g=n1;break;case"focusin":v="focus",g=za;break;case"focusout":v="blur",g=za;break;case"beforeblur":case"afterblur":g=za;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Hx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=i1;break;case rg:case sg:case ig:g=Gx;break;case og:g=a1;break;case"scroll":g=Ux;break;case"wheel":g=u1;break;case"copy":case"cut":case"paste":g=Qx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=mf}var x=(t&4)!==0,S=!x&&e==="scroll",p=x?f!==null?f+"Capture":null:f;x=[];for(var m=c,y;m!==null;){y=m;var k=y.stateNode;if(y.tag===5&&k!==null&&(y=k,p!==null&&(k=Us(m,p),k!=null&&x.push(Qs(m,k,y)))),S)break;m=m.return}0<x.length&&(f=new g(f,v,null,n,d),h.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==Ol&&(v=n.relatedTarget||n.fromElement)&&(Gn(v)||v[tn]))break e;if((g||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=c,v=v?Gn(v):null,v!==null&&(S=dr(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=c),g!==v)){if(x=ff,k="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(x=mf,k="onPointerLeave",p="onPointerEnter",m="pointer"),S=g==null?f:br(g),y=v==null?f:br(v),f=new x(k,m+"leave",g,n,d),f.target=S,f.relatedTarget=y,k=null,Gn(d)===c&&(x=new x(p,m+"enter",v,n,d),x.target=y,x.relatedTarget=S,k=x),S=k,g&&v)t:{for(x=g,p=v,m=0,y=x;y;y=pr(y))m++;for(y=0,k=p;k;k=pr(k))y++;for(;0<m-y;)x=pr(x),m--;for(;0<y-m;)p=pr(p),y--;for(;m--;){if(x===p||p!==null&&x===p.alternate)break t;x=pr(x),p=pr(p)}x=null}else x=null;g!==null&&jf(h,f,g,x,!1),v!==null&&S!==null&&jf(h,S,v,x,!0)}}e:{if(f=c?br(c):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var j=g1;else if(yf(f))if(Zp)j=w1;else{j=v1;var C=y1}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(j=x1);if(j&&(j=j(e,c))){Yp(h,j,n,d);break e}C&&C(e,f,c),e==="focusout"&&(C=f._wrapperState)&&C.controlled&&f.type==="number"&&Rl(f,"number",f.value)}switch(C=c?br(c):window,e){case"focusin":(yf(C)||C.contentEditable==="true")&&(wr=C,Kl=c,Es=null);break;case"focusout":Es=Kl=wr=null;break;case"mousedown":Gl=!0;break;case"contextmenu":case"mouseup":case"dragend":Gl=!1,bf(h,n,d);break;case"selectionchange":if(k1)break;case"keydown":case"keyup":bf(h,n,d)}var _;if(uc)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else xr?Qp(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(qp&&n.locale!=="ko"&&(xr||T!=="onCompositionStart"?T==="onCompositionEnd"&&xr&&(_=Gp()):(gn=d,oc="value"in gn?gn.value:gn.textContent,xr=!0)),C=wo(c,T),0<C.length&&(T=new hf(T,e,null,n,d),h.push({event:T,listeners:C}),_?T.data=_:(_=Xp(n),_!==null&&(T.data=_)))),(_=d1?f1(e,n):h1(e,n))&&(c=wo(c,"onBeforeInput"),0<c.length&&(d=new hf("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=_))}lg(h,t)})}function Qs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function wo(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Us(e,n),i!=null&&r.unshift(Qs(e,i,s)),i=Us(e,t),i!=null&&r.push(Qs(e,i,s))),e=e.return}return r}function pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function jf(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,s?(u=Us(n,i),u!=null&&o.unshift(Qs(n,u,a))):s||(u=Us(n,i),u!=null&&o.push(Qs(n,u,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var N1=/\r\n?/g,_1=/\u0000|\uFFFD/g;function Cf(e){return(typeof e=="string"?e:""+e).replace(N1,`
`).replace(_1,"")}function Ri(e,t,n){if(t=Cf(t),Cf(e)!==t&&n)throw Error(V(425))}function So(){}var ql=null,Ql=null;function Xl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yl=typeof setTimeout=="function"?setTimeout:void 0,E1=typeof clearTimeout=="function"?clearTimeout:void 0,Nf=typeof Promise=="function"?Promise:void 0,P1=typeof queueMicrotask=="function"?queueMicrotask:typeof Nf<"u"?function(e){return Nf.resolve(null).then(e).catch(A1)}:Yl;function A1(e){setTimeout(function(){throw e})}function qa(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Ws(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Ws(t)}function Sn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function _f(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ts=Math.random().toString(36).slice(2),Rt="__reactFiber$"+ts,Xs="__reactProps$"+ts,tn="__reactContainer$"+ts,Zl="__reactEvents$"+ts,D1="__reactListeners$"+ts,M1="__reactHandles$"+ts;function Gn(e){var t=e[Rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[tn]||n[Rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=_f(e);e!==null;){if(n=e[Rt])return n;e=_f(e)}return t}e=n,n=e.parentNode}return null}function gi(e){return e=e[Rt]||e[tn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function br(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(V(33))}function sa(e){return e[Xs]||null}var Jl=[],kr=-1;function Dn(e){return{current:e}}function le(e){0>kr||(e.current=Jl[kr],Jl[kr]=null,kr--)}function ie(e,t){kr++,Jl[kr]=e.current,e.current=t}var Nn={},$e=Dn(Nn),Ye=Dn(!1),rr=Nn;function $r(e,t){var n=e.type.contextTypes;if(!n)return Nn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ze(e){return e=e.childContextTypes,e!=null}function bo(){le(Ye),le($e)}function Ef(e,t,n){if($e.current!==Nn)throw Error(V(168));ie($e,t),ie(Ye,n)}function cg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(V(108,yx(e)||"Unknown",s));return ge({},n,r)}function ko(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Nn,rr=$e.current,ie($e,e),ie(Ye,Ye.current),!0}function Pf(e,t,n){var r=e.stateNode;if(!r)throw Error(V(169));n?(e=cg(e,t,rr),r.__reactInternalMemoizedMergedChildContext=e,le(Ye),le($e),ie($e,e)):le(Ye),ie(Ye,n)}var Wt=null,ia=!1,Qa=!1;function dg(e){Wt===null?Wt=[e]:Wt.push(e)}function R1(e){ia=!0,dg(e)}function Mn(){if(!Qa&&Wt!==null){Qa=!0;var e=0,t=ne;try{var n=Wt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Wt=null,ia=!1}catch(s){throw Wt!==null&&(Wt=Wt.slice(e+1)),Vp(nc,Mn),s}finally{ne=t,Qa=!1}}return null}var Tr=[],jr=0,To=null,jo=0,mt=[],pt=0,sr=null,qt=1,Qt="";function zn(e,t){Tr[jr++]=jo,Tr[jr++]=To,To=e,jo=t}function fg(e,t,n){mt[pt++]=qt,mt[pt++]=Qt,mt[pt++]=sr,sr=e;var r=qt;e=Qt;var s=32-Et(r)-1;r&=~(1<<s),n+=1;var i=32-Et(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,qt=1<<32-Et(t)+s|n<<s|r,Qt=i+e}else qt=1<<i|n<<s|r,Qt=e}function dc(e){e.return!==null&&(zn(e,1),fg(e,1,0))}function fc(e){for(;e===To;)To=Tr[--jr],Tr[jr]=null,jo=Tr[--jr],Tr[jr]=null;for(;e===sr;)sr=mt[--pt],mt[pt]=null,Qt=mt[--pt],mt[pt]=null,qt=mt[--pt],mt[pt]=null}var rt=null,nt=null,de=!1,jt=null;function hg(e,t){var n=gt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Af(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,rt=e,nt=Sn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,rt=e,nt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=sr!==null?{id:qt,overflow:Qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=gt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,rt=e,nt=null,!0):!1;default:return!1}}function eu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function tu(e){if(de){var t=nt;if(t){var n=t;if(!Af(e,t)){if(eu(e))throw Error(V(418));t=Sn(n.nextSibling);var r=rt;t&&Af(e,t)?hg(r,n):(e.flags=e.flags&-4097|2,de=!1,rt=e)}}else{if(eu(e))throw Error(V(418));e.flags=e.flags&-4097|2,de=!1,rt=e}}}function Df(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;rt=e}function Li(e){if(e!==rt)return!1;if(!de)return Df(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Xl(e.type,e.memoizedProps)),t&&(t=nt)){if(eu(e))throw mg(),Error(V(418));for(;t;)hg(e,t),t=Sn(t.nextSibling)}if(Df(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(V(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){nt=Sn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}nt=null}}else nt=rt?Sn(e.stateNode.nextSibling):null;return!0}function mg(){for(var e=nt;e;)e=Sn(e.nextSibling)}function Ur(){nt=rt=null,de=!1}function hc(e){jt===null?jt=[e]:jt.push(e)}var L1=on.ReactCurrentBatchConfig;function cs(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(V(309));var r=n.stateNode}if(!r)throw Error(V(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(V(284));if(!n._owner)throw Error(V(290,e))}return e}function Vi(e,t){throw e=Object.prototype.toString.call(t),Error(V(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Mf(e){var t=e._init;return t(e._payload)}function pg(e){function t(p,m){if(e){var y=p.deletions;y===null?(p.deletions=[m],p.flags|=16):y.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function s(p,m){return p=jn(p,m),p.index=0,p.sibling=null,p}function i(p,m,y){return p.index=y,e?(y=p.alternate,y!==null?(y=y.index,y<m?(p.flags|=2,m):y):(p.flags|=2,m)):(p.flags|=1048576,m)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,m,y,k){return m===null||m.tag!==6?(m=nl(y,p.mode,k),m.return=p,m):(m=s(m,y),m.return=p,m)}function u(p,m,y,k){var j=y.type;return j===vr?d(p,m,y.props.children,k,y.key):m!==null&&(m.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===fn&&Mf(j)===m.type)?(k=s(m,y.props),k.ref=cs(p,m,y),k.return=p,k):(k=oo(y.type,y.key,y.props,null,p.mode,k),k.ref=cs(p,m,y),k.return=p,k)}function c(p,m,y,k){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=rl(y,p.mode,k),m.return=p,m):(m=s(m,y.children||[]),m.return=p,m)}function d(p,m,y,k,j){return m===null||m.tag!==7?(m=Jn(y,p.mode,k,j),m.return=p,m):(m=s(m,y),m.return=p,m)}function h(p,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=nl(""+m,p.mode,y),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ji:return y=oo(m.type,m.key,m.props,null,p.mode,y),y.ref=cs(p,null,m),y.return=p,y;case yr:return m=rl(m,p.mode,y),m.return=p,m;case fn:var k=m._init;return h(p,k(m._payload),y)}if(vs(m)||is(m))return m=Jn(m,p.mode,y,null),m.return=p,m;Vi(p,m)}return null}function f(p,m,y,k){var j=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return j!==null?null:a(p,m,""+y,k);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ji:return y.key===j?u(p,m,y,k):null;case yr:return y.key===j?c(p,m,y,k):null;case fn:return j=y._init,f(p,m,j(y._payload),k)}if(vs(y)||is(y))return j!==null?null:d(p,m,y,k,null);Vi(p,y)}return null}function g(p,m,y,k,j){if(typeof k=="string"&&k!==""||typeof k=="number")return p=p.get(y)||null,a(m,p,""+k,j);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case ji:return p=p.get(k.key===null?y:k.key)||null,u(m,p,k,j);case yr:return p=p.get(k.key===null?y:k.key)||null,c(m,p,k,j);case fn:var C=k._init;return g(p,m,y,C(k._payload),j)}if(vs(k)||is(k))return p=p.get(y)||null,d(m,p,k,j,null);Vi(m,k)}return null}function v(p,m,y,k){for(var j=null,C=null,_=m,T=m=0,E=null;_!==null&&T<y.length;T++){_.index>T?(E=_,_=null):E=_.sibling;var A=f(p,_,y[T],k);if(A===null){_===null&&(_=E);break}e&&_&&A.alternate===null&&t(p,_),m=i(A,m,T),C===null?j=A:C.sibling=A,C=A,_=E}if(T===y.length)return n(p,_),de&&zn(p,T),j;if(_===null){for(;T<y.length;T++)_=h(p,y[T],k),_!==null&&(m=i(_,m,T),C===null?j=_:C.sibling=_,C=_);return de&&zn(p,T),j}for(_=r(p,_);T<y.length;T++)E=g(_,p,T,y[T],k),E!==null&&(e&&E.alternate!==null&&_.delete(E.key===null?T:E.key),m=i(E,m,T),C===null?j=E:C.sibling=E,C=E);return e&&_.forEach(function(R){return t(p,R)}),de&&zn(p,T),j}function x(p,m,y,k){var j=is(y);if(typeof j!="function")throw Error(V(150));if(y=j.call(y),y==null)throw Error(V(151));for(var C=j=null,_=m,T=m=0,E=null,A=y.next();_!==null&&!A.done;T++,A=y.next()){_.index>T?(E=_,_=null):E=_.sibling;var R=f(p,_,A.value,k);if(R===null){_===null&&(_=E);break}e&&_&&R.alternate===null&&t(p,_),m=i(R,m,T),C===null?j=R:C.sibling=R,C=R,_=E}if(A.done)return n(p,_),de&&zn(p,T),j;if(_===null){for(;!A.done;T++,A=y.next())A=h(p,A.value,k),A!==null&&(m=i(A,m,T),C===null?j=A:C.sibling=A,C=A);return de&&zn(p,T),j}for(_=r(p,_);!A.done;T++,A=y.next())A=g(_,p,T,A.value,k),A!==null&&(e&&A.alternate!==null&&_.delete(A.key===null?T:A.key),m=i(A,m,T),C===null?j=A:C.sibling=A,C=A);return e&&_.forEach(function(D){return t(p,D)}),de&&zn(p,T),j}function S(p,m,y,k){if(typeof y=="object"&&y!==null&&y.type===vr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ji:e:{for(var j=y.key,C=m;C!==null;){if(C.key===j){if(j=y.type,j===vr){if(C.tag===7){n(p,C.sibling),m=s(C,y.props.children),m.return=p,p=m;break e}}else if(C.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===fn&&Mf(j)===C.type){n(p,C.sibling),m=s(C,y.props),m.ref=cs(p,C,y),m.return=p,p=m;break e}n(p,C);break}else t(p,C);C=C.sibling}y.type===vr?(m=Jn(y.props.children,p.mode,k,y.key),m.return=p,p=m):(k=oo(y.type,y.key,y.props,null,p.mode,k),k.ref=cs(p,m,y),k.return=p,p=k)}return o(p);case yr:e:{for(C=y.key;m!==null;){if(m.key===C)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(p,m.sibling),m=s(m,y.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=rl(y,p.mode,k),m.return=p,p=m}return o(p);case fn:return C=y._init,S(p,m,C(y._payload),k)}if(vs(y))return v(p,m,y,k);if(is(y))return x(p,m,y,k);Vi(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(p,m.sibling),m=s(m,y),m.return=p,p=m):(n(p,m),m=nl(y,p.mode,k),m.return=p,p=m),o(p)):n(p,m)}return S}var Br=pg(!0),gg=pg(!1),Co=Dn(null),No=null,Cr=null,mc=null;function pc(){mc=Cr=No=null}function gc(e){var t=Co.current;le(Co),e._currentValue=t}function nu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ir(e,t){No=e,mc=Cr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Xe=!0),e.firstContext=null)}function vt(e){var t=e._currentValue;if(mc!==e)if(e={context:e,memoizedValue:t,next:null},Cr===null){if(No===null)throw Error(V(308));Cr=e,No.dependencies={lanes:0,firstContext:e}}else Cr=Cr.next=e;return t}var qn=null;function yc(e){qn===null?qn=[e]:qn.push(e)}function yg(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,yc(t)):(n.next=s.next,s.next=n),t.interleaved=n,nn(e,r)}function nn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var hn=!1;function vc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function vg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Xt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ee&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,nn(e,n)}return s=r.interleaved,s===null?(t.next=t,yc(r)):(t.next=s.next,s.next=t),r.interleaved=t,nn(e,n)}function eo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,rc(e,n)}}function Rf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function _o(e,t,n,r){var s=e.updateQueue;hn=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=u))}if(i!==null){var h=s.baseState;o=0,d=c=u=null,a=i;do{var f=a.lane,g=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){h=v.call(g,h,f);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(g,h,f):v,f==null)break e;h=ge({},h,f);break e;case 2:hn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else g={eventTime:g,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=g,u=h):d=d.next=g,o|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(u=h),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);or|=o,e.lanes=o,e.memoizedState=h}}function Lf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(V(191,s));s.call(r)}}}var yi={},Ft=Dn(yi),Ys=Dn(yi),Zs=Dn(yi);function Qn(e){if(e===yi)throw Error(V(174));return e}function xc(e,t){switch(ie(Zs,t),ie(Ys,e),ie(Ft,yi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Vl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Vl(t,e)}le(Ft),ie(Ft,t)}function Hr(){le(Ft),le(Ys),le(Zs)}function xg(e){Qn(Zs.current);var t=Qn(Ft.current),n=Vl(t,e.type);t!==n&&(ie(Ys,e),ie(Ft,n))}function wc(e){Ys.current===e&&(le(Ft),le(Ys))}var he=Dn(0);function Eo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Xa=[];function Sc(){for(var e=0;e<Xa.length;e++)Xa[e]._workInProgressVersionPrimary=null;Xa.length=0}var to=on.ReactCurrentDispatcher,Ya=on.ReactCurrentBatchConfig,ir=0,pe=null,Ne=null,Ee=null,Po=!1,Ps=!1,Js=0,V1=0;function Le(){throw Error(V(321))}function bc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!At(e[n],t[n]))return!1;return!0}function kc(e,t,n,r,s,i){if(ir=i,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,to.current=e===null||e.memoizedState===null?z1:$1,e=n(r,s),Ps){i=0;do{if(Ps=!1,Js=0,25<=i)throw Error(V(301));i+=1,Ee=Ne=null,t.updateQueue=null,to.current=U1,e=n(r,s)}while(Ps)}if(to.current=Ao,t=Ne!==null&&Ne.next!==null,ir=0,Ee=Ne=pe=null,Po=!1,t)throw Error(V(300));return e}function Tc(){var e=Js!==0;return Js=0,e}function Mt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function xt(){if(Ne===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ne.next;var t=Ee===null?pe.memoizedState:Ee.next;if(t!==null)Ee=t,Ne=e;else{if(e===null)throw Error(V(310));Ne=e,e={memoizedState:Ne.memoizedState,baseState:Ne.baseState,baseQueue:Ne.baseQueue,queue:Ne.queue,next:null},Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function ei(e,t){return typeof t=="function"?t(e):t}function Za(e){var t=xt(),n=t.queue;if(n===null)throw Error(V(311));n.lastRenderedReducer=e;var r=Ne,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,u=null,c=i;do{var d=c.lane;if((ir&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=h,o=r):u=u.next=h,pe.lanes|=d,or|=d}c=c.next}while(c!==null&&c!==i);u===null?o=r:u.next=a,At(r,t.memoizedState)||(Xe=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,pe.lanes|=i,or|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ja(e){var t=xt(),n=t.queue;if(n===null)throw Error(V(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);At(i,t.memoizedState)||(Xe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function wg(){}function Sg(e,t){var n=pe,r=xt(),s=t(),i=!At(r.memoizedState,s);if(i&&(r.memoizedState=s,Xe=!0),r=r.queue,jc(Tg.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,ti(9,kg.bind(null,n,r,s,t),void 0,null),Pe===null)throw Error(V(349));ir&30||bg(n,t,s)}return s}function bg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kg(e,t,n,r){t.value=n,t.getSnapshot=r,jg(t)&&Cg(e)}function Tg(e,t,n){return n(function(){jg(t)&&Cg(e)})}function jg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!At(e,n)}catch{return!0}}function Cg(e){var t=nn(e,1);t!==null&&Pt(t,e,1,-1)}function Vf(e){var t=Mt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ei,lastRenderedState:e},t.queue=e,e=e.dispatch=O1.bind(null,pe,e),[t.memoizedState,e]}function ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ng(){return xt().memoizedState}function no(e,t,n,r){var s=Mt();pe.flags|=e,s.memoizedState=ti(1|t,n,void 0,r===void 0?null:r)}function oa(e,t,n,r){var s=xt();r=r===void 0?null:r;var i=void 0;if(Ne!==null){var o=Ne.memoizedState;if(i=o.destroy,r!==null&&bc(r,o.deps)){s.memoizedState=ti(t,n,i,r);return}}pe.flags|=e,s.memoizedState=ti(1|t,n,i,r)}function Ff(e,t){return no(8390656,8,e,t)}function jc(e,t){return oa(2048,8,e,t)}function _g(e,t){return oa(4,2,e,t)}function Eg(e,t){return oa(4,4,e,t)}function Pg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ag(e,t,n){return n=n!=null?n.concat([e]):null,oa(4,4,Pg.bind(null,t,e),n)}function Cc(){}function Dg(e,t){var n=xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Mg(e,t){var n=xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Rg(e,t,n){return ir&21?(At(n,t)||(n=Op(),pe.lanes|=n,or|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Xe=!0),e.memoizedState=n)}function F1(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=Ya.transition;Ya.transition={};try{e(!1),t()}finally{ne=n,Ya.transition=r}}function Lg(){return xt().memoizedState}function I1(e,t,n){var r=Tn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Vg(e))Fg(t,n);else if(n=yg(e,t,n,r),n!==null){var s=We();Pt(n,e,r,s),Ig(n,t,r)}}function O1(e,t,n){var r=Tn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Vg(e))Fg(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,At(a,o)){var u=t.interleaved;u===null?(s.next=s,yc(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=yg(e,t,s,r),n!==null&&(s=We(),Pt(n,e,r,s),Ig(n,t,r))}}function Vg(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Fg(e,t){Ps=Po=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ig(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,rc(e,n)}}var Ao={readContext:vt,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useInsertionEffect:Le,useLayoutEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useMutableSource:Le,useSyncExternalStore:Le,useId:Le,unstable_isNewReconciler:!1},z1={readContext:vt,useCallback:function(e,t){return Mt().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:Ff,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,no(4194308,4,Pg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return no(4194308,4,e,t)},useInsertionEffect:function(e,t){return no(4,2,e,t)},useMemo:function(e,t){var n=Mt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Mt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=I1.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Mt();return e={current:e},t.memoizedState=e},useState:Vf,useDebugValue:Cc,useDeferredValue:function(e){return Mt().memoizedState=e},useTransition:function(){var e=Vf(!1),t=e[0];return e=F1.bind(null,e[1]),Mt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,s=Mt();if(de){if(n===void 0)throw Error(V(407));n=n()}else{if(n=t(),Pe===null)throw Error(V(349));ir&30||bg(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Ff(Tg.bind(null,r,i,e),[e]),r.flags|=2048,ti(9,kg.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Mt(),t=Pe.identifierPrefix;if(de){var n=Qt,r=qt;n=(r&~(1<<32-Et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Js++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=V1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$1={readContext:vt,useCallback:Dg,useContext:vt,useEffect:jc,useImperativeHandle:Ag,useInsertionEffect:_g,useLayoutEffect:Eg,useMemo:Mg,useReducer:Za,useRef:Ng,useState:function(){return Za(ei)},useDebugValue:Cc,useDeferredValue:function(e){var t=xt();return Rg(t,Ne.memoizedState,e)},useTransition:function(){var e=Za(ei)[0],t=xt().memoizedState;return[e,t]},useMutableSource:wg,useSyncExternalStore:Sg,useId:Lg,unstable_isNewReconciler:!1},U1={readContext:vt,useCallback:Dg,useContext:vt,useEffect:jc,useImperativeHandle:Ag,useInsertionEffect:_g,useLayoutEffect:Eg,useMemo:Mg,useReducer:Ja,useRef:Ng,useState:function(){return Ja(ei)},useDebugValue:Cc,useDeferredValue:function(e){var t=xt();return Ne===null?t.memoizedState=e:Rg(t,Ne.memoizedState,e)},useTransition:function(){var e=Ja(ei)[0],t=xt().memoizedState;return[e,t]},useMutableSource:wg,useSyncExternalStore:Sg,useId:Lg,unstable_isNewReconciler:!1};function kt(e,t){if(e&&e.defaultProps){t=ge({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ru(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ge({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var aa={isMounted:function(e){return(e=e._reactInternals)?dr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=We(),s=Tn(e),i=Xt(r,s);i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,s),t!==null&&(Pt(t,e,s,r),eo(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=We(),s=Tn(e),i=Xt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,s),t!==null&&(Pt(t,e,s,r),eo(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=We(),r=Tn(e),s=Xt(n,r);s.tag=2,t!=null&&(s.callback=t),t=bn(e,s,r),t!==null&&(Pt(t,e,r,n),eo(t,e,r))}};function If(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Gs(n,r)||!Gs(s,i):!0}function Og(e,t,n){var r=!1,s=Nn,i=t.contextType;return typeof i=="object"&&i!==null?i=vt(i):(s=Ze(t)?rr:$e.current,r=t.contextTypes,i=(r=r!=null)?$r(e,s):Nn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=aa,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Of(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&aa.enqueueReplaceState(t,t.state,null)}function su(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},vc(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=vt(i):(i=Ze(t)?rr:$e.current,s.context=$r(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ru(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&aa.enqueueReplaceState(s,s.state,null),_o(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Wr(e,t){try{var n="",r=t;do n+=gx(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function el(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function iu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var B1=typeof WeakMap=="function"?WeakMap:Map;function zg(e,t,n){n=Xt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Mo||(Mo=!0,pu=r),iu(e,t)},n}function $g(e,t,n){n=Xt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){iu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){iu(e,t),typeof r!="function"&&(kn===null?kn=new Set([this]):kn.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function zf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new B1;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=rw.bind(null,e,t,n),t.then(e,e))}function $f(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Uf(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Xt(-1,1),t.tag=2,bn(n,t,1))),n.lanes|=1),e)}var H1=on.ReactCurrentOwner,Xe=!1;function Ue(e,t,n,r){t.child=e===null?gg(t,null,n,r):Br(t,e.child,n,r)}function Bf(e,t,n,r,s){n=n.render;var i=t.ref;return Ir(t,s),r=kc(e,t,n,r,i,s),n=Tc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(de&&n&&dc(t),t.flags|=1,Ue(e,t,r,s),t.child)}function Hf(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Rc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Ug(e,t,i,r,s)):(e=oo(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Gs,n(o,r)&&e.ref===t.ref)return rn(e,t,s)}return t.flags|=1,e=jn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Ug(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Gs(i,r)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(Xe=!0);else return t.lanes=e.lanes,rn(e,t,s)}return ou(e,t,n,r,s)}function Bg(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ie(_r,et),et|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ie(_r,et),et|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ie(_r,et),et|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ie(_r,et),et|=r;return Ue(e,t,s,n),t.child}function Hg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ou(e,t,n,r,s){var i=Ze(n)?rr:$e.current;return i=$r(t,i),Ir(t,s),n=kc(e,t,n,r,i,s),r=Tc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(de&&r&&dc(t),t.flags|=1,Ue(e,t,n,s),t.child)}function Wf(e,t,n,r,s){if(Ze(n)){var i=!0;ko(t)}else i=!1;if(Ir(t,s),t.stateNode===null)ro(e,t),Og(t,n,r),su(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=vt(c):(c=Ze(n)?rr:$e.current,c=$r(t,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||u!==c)&&Of(t,o,r,c),hn=!1;var f=t.memoizedState;o.state=f,_o(t,r,o,s),u=t.memoizedState,a!==r||f!==u||Ye.current||hn?(typeof d=="function"&&(ru(t,n,d,r),u=t.memoizedState),(a=hn||If(t,n,a,r,f,u,c))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,vg(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:kt(t.type,a),o.props=c,h=t.pendingProps,f=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=vt(u):(u=Ze(n)?rr:$e.current,u=$r(t,u));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==u)&&Of(t,o,r,u),hn=!1,f=t.memoizedState,o.state=f,_o(t,r,o,s);var v=t.memoizedState;a!==h||f!==v||Ye.current||hn?(typeof g=="function"&&(ru(t,n,g,r),v=t.memoizedState),(c=hn||If(t,n,c,r,f,v,u)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return au(e,t,n,r,i,s)}function au(e,t,n,r,s,i){Hg(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Pf(t,n,!1),rn(e,t,i);r=t.stateNode,H1.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Br(t,e.child,null,i),t.child=Br(t,null,a,i)):Ue(e,t,a,i),t.memoizedState=r.state,s&&Pf(t,n,!0),t.child}function Wg(e){var t=e.stateNode;t.pendingContext?Ef(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ef(e,t.context,!1),xc(e,t.containerInfo)}function Kf(e,t,n,r,s){return Ur(),hc(s),t.flags|=256,Ue(e,t,n,r),t.child}var lu={dehydrated:null,treeContext:null,retryLane:0};function uu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Kg(e,t,n){var r=t.pendingProps,s=he.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ie(he,s&1),e===null)return tu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=ca(o,r,0,null),e=Jn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=uu(n),t.memoizedState=lu,e):Nc(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return W1(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=jn(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=jn(a,i):(i=Jn(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?uu(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=lu,r}return i=e.child,e=i.sibling,r=jn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Nc(e,t){return t=ca({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fi(e,t,n,r){return r!==null&&hc(r),Br(t,e.child,null,n),e=Nc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function W1(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=el(Error(V(422))),Fi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=ca({mode:"visible",children:r.children},s,0,null),i=Jn(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Br(t,e.child,null,o),t.child.memoizedState=uu(o),t.memoizedState=lu,i);if(!(t.mode&1))return Fi(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(V(419)),r=el(i,r,void 0),Fi(e,t,o,r)}if(a=(o&e.childLanes)!==0,Xe||a){if(r=Pe,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,nn(e,s),Pt(r,e,s,-1))}return Mc(),r=el(Error(V(421))),Fi(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=sw.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,nt=Sn(s.nextSibling),rt=t,de=!0,jt=null,e!==null&&(mt[pt++]=qt,mt[pt++]=Qt,mt[pt++]=sr,qt=e.id,Qt=e.overflow,sr=t),t=Nc(t,r.children),t.flags|=4096,t)}function Gf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),nu(e.return,t,n)}function tl(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Gg(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(Ue(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gf(e,n,t);else if(e.tag===19)Gf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ie(he,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Eo(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),tl(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Eo(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}tl(t,!0,n,null,i);break;case"together":tl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ro(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function rn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),or|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(V(153));if(t.child!==null){for(e=t.child,n=jn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function K1(e,t,n){switch(t.tag){case 3:Wg(t),Ur();break;case 5:xg(t);break;case 1:Ze(t.type)&&ko(t);break;case 4:xc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;ie(Co,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ie(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Kg(e,t,n):(ie(he,he.current&1),e=rn(e,t,n),e!==null?e.sibling:null);ie(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Gg(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ie(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,Bg(e,t,n)}return rn(e,t,n)}var qg,cu,Qg,Xg;qg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};cu=function(){};Qg=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Qn(Ft.current);var i=null;switch(n){case"input":s=Dl(e,s),r=Dl(e,r),i=[];break;case"select":s=ge({},s,{value:void 0}),r=ge({},r,{value:void 0}),i=[];break;case"textarea":s=Ll(e,s),r=Ll(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=So)}Fl(n,r);var o;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(zs.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(zs.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&oe("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Xg=function(e,t,n,r){n!==r&&(t.flags|=4)};function ds(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function G1(e,t,n){var r=t.pendingProps;switch(fc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ve(t),null;case 1:return Ze(t.type)&&bo(),Ve(t),null;case 3:return r=t.stateNode,Hr(),le(Ye),le($e),Sc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Li(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,jt!==null&&(vu(jt),jt=null))),cu(e,t),Ve(t),null;case 5:wc(t);var s=Qn(Zs.current);if(n=t.type,e!==null&&t.stateNode!=null)Qg(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(V(166));return Ve(t),null}if(e=Qn(Ft.current),Li(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Rt]=t,r[Xs]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(s=0;s<ws.length;s++)oe(ws[s],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":tf(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":rf(r,i),oe("invalid",r)}Fl(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Ri(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Ri(r.textContent,a,e),s=["children",""+a]):zs.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&oe("scroll",r)}switch(n){case"input":Ci(r),nf(r,i,!0);break;case"textarea":Ci(r),sf(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=So)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Tp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Rt]=t,e[Xs]=r,qg(e,t,!1,!1),t.stateNode=e;e:{switch(o=Il(n,r),n){case"dialog":oe("cancel",e),oe("close",e),s=r;break;case"iframe":case"object":case"embed":oe("load",e),s=r;break;case"video":case"audio":for(s=0;s<ws.length;s++)oe(ws[s],e);s=r;break;case"source":oe("error",e),s=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),s=r;break;case"details":oe("toggle",e),s=r;break;case"input":tf(e,r),s=Dl(e,r),oe("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=ge({},r,{value:void 0}),oe("invalid",e);break;case"textarea":rf(e,r),s=Ll(e,r),oe("invalid",e);break;default:s=r}Fl(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?Np(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&jp(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&$s(e,u):typeof u=="number"&&$s(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(zs.hasOwnProperty(i)?u!=null&&i==="onScroll"&&oe("scroll",e):u!=null&&Yu(e,i,u,o))}switch(n){case"input":Ci(e),nf(e,r,!1);break;case"textarea":Ci(e),sf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Cn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Rr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Rr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=So)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ve(t),null;case 6:if(e&&t.stateNode!=null)Xg(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(V(166));if(n=Qn(Zs.current),Qn(Ft.current),Li(t)){if(r=t.stateNode,n=t.memoizedProps,r[Rt]=t,(i=r.nodeValue!==n)&&(e=rt,e!==null))switch(e.tag){case 3:Ri(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ri(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Rt]=t,t.stateNode=r}return Ve(t),null;case 13:if(le(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&nt!==null&&t.mode&1&&!(t.flags&128))mg(),Ur(),t.flags|=98560,i=!1;else if(i=Li(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(V(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(V(317));i[Rt]=t}else Ur(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ve(t),i=!1}else jt!==null&&(vu(jt),jt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?_e===0&&(_e=3):Mc())),t.updateQueue!==null&&(t.flags|=4),Ve(t),null);case 4:return Hr(),cu(e,t),e===null&&qs(t.stateNode.containerInfo),Ve(t),null;case 10:return gc(t.type._context),Ve(t),null;case 17:return Ze(t.type)&&bo(),Ve(t),null;case 19:if(le(he),i=t.memoizedState,i===null)return Ve(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)ds(i,!1);else{if(_e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Eo(e),o!==null){for(t.flags|=128,ds(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ie(he,he.current&1|2),t.child}e=e.sibling}i.tail!==null&&Se()>Kr&&(t.flags|=128,r=!0,ds(i,!1),t.lanes=4194304)}else{if(!r)if(e=Eo(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ds(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!de)return Ve(t),null}else 2*Se()-i.renderingStartTime>Kr&&n!==1073741824&&(t.flags|=128,r=!0,ds(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Se(),t.sibling=null,n=he.current,ie(he,r?n&1|2:n&1),t):(Ve(t),null);case 22:case 23:return Dc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?et&1073741824&&(Ve(t),t.subtreeFlags&6&&(t.flags|=8192)):Ve(t),null;case 24:return null;case 25:return null}throw Error(V(156,t.tag))}function q1(e,t){switch(fc(t),t.tag){case 1:return Ze(t.type)&&bo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Hr(),le(Ye),le($e),Sc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return wc(t),null;case 13:if(le(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(V(340));Ur()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(he),null;case 4:return Hr(),null;case 10:return gc(t.type._context),null;case 22:case 23:return Dc(),null;case 24:return null;default:return null}}var Ii=!1,Ie=!1,Q1=typeof WeakSet=="function"?WeakSet:Set,B=null;function Nr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function du(e,t,n){try{n()}catch(r){ve(e,t,r)}}var qf=!1;function X1(e,t){if(ql=vo,e=tg(),cc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,u=-1,c=0,d=0,h=e,f=null;t:for(;;){for(var g;h!==n||s!==0&&h.nodeType!==3||(a=o+s),h!==i||r!==0&&h.nodeType!==3||(u=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)f=h,h=g;for(;;){if(h===e)break t;if(f===n&&++c===s&&(a=o),f===i&&++d===r&&(u=o),(g=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=g}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ql={focusedElem:e,selectionRange:n},vo=!1,B=t;B!==null;)if(t=B,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,B=e;else for(;B!==null;){t=B;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?x:kt(t.type,x),S);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(V(163))}}catch(k){ve(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,B=e;break}B=t.return}return v=qf,qf=!1,v}function As(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&du(t,n,i)}s=s.next}while(s!==r)}}function la(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function fu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yg(e){var t=e.alternate;t!==null&&(e.alternate=null,Yg(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[Xs],delete t[Zl],delete t[D1],delete t[M1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Zg(e){return e.tag===5||e.tag===3||e.tag===4}function Qf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Zg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=So));else if(r!==4&&(e=e.child,e!==null))for(hu(e,t,n),e=e.sibling;e!==null;)hu(e,t,n),e=e.sibling}function mu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(mu(e,t,n),e=e.sibling;e!==null;)mu(e,t,n),e=e.sibling}var Ae=null,Tt=!1;function un(e,t,n){for(n=n.child;n!==null;)Jg(e,t,n),n=n.sibling}function Jg(e,t,n){if(Vt&&typeof Vt.onCommitFiberUnmount=="function")try{Vt.onCommitFiberUnmount(ea,n)}catch{}switch(n.tag){case 5:Ie||Nr(n,t);case 6:var r=Ae,s=Tt;Ae=null,un(e,t,n),Ae=r,Tt=s,Ae!==null&&(Tt?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(Tt?(e=Ae,n=n.stateNode,e.nodeType===8?qa(e.parentNode,n):e.nodeType===1&&qa(e,n),Ws(e)):qa(Ae,n.stateNode));break;case 4:r=Ae,s=Tt,Ae=n.stateNode.containerInfo,Tt=!0,un(e,t,n),Ae=r,Tt=s;break;case 0:case 11:case 14:case 15:if(!Ie&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&du(n,t,o),s=s.next}while(s!==r)}un(e,t,n);break;case 1:if(!Ie&&(Nr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ve(n,t,a)}un(e,t,n);break;case 21:un(e,t,n);break;case 22:n.mode&1?(Ie=(r=Ie)||n.memoizedState!==null,un(e,t,n),Ie=r):un(e,t,n);break;default:un(e,t,n)}}function Xf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Q1),t.forEach(function(r){var s=iw.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function St(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:Ae=a.stateNode,Tt=!1;break e;case 3:Ae=a.stateNode.containerInfo,Tt=!0;break e;case 4:Ae=a.stateNode.containerInfo,Tt=!0;break e}a=a.return}if(Ae===null)throw Error(V(160));Jg(i,o,s),Ae=null,Tt=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){ve(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)e0(t,e),t=t.sibling}function e0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Dt(e),r&4){try{As(3,e,e.return),la(3,e)}catch(x){ve(e,e.return,x)}try{As(5,e,e.return)}catch(x){ve(e,e.return,x)}}break;case 1:St(t,e),Dt(e),r&512&&n!==null&&Nr(n,n.return);break;case 5:if(St(t,e),Dt(e),r&512&&n!==null&&Nr(n,n.return),e.flags&32){var s=e.stateNode;try{$s(s,"")}catch(x){ve(e,e.return,x)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&bp(s,i),Il(a,o);var c=Il(a,i);for(o=0;o<u.length;o+=2){var d=u[o],h=u[o+1];d==="style"?Np(s,h):d==="dangerouslySetInnerHTML"?jp(s,h):d==="children"?$s(s,h):Yu(s,d,h,c)}switch(a){case"input":Ml(s,i);break;case"textarea":kp(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Rr(s,!!i.multiple,g,!1):f!==!!i.multiple&&(i.defaultValue!=null?Rr(s,!!i.multiple,i.defaultValue,!0):Rr(s,!!i.multiple,i.multiple?[]:"",!1))}s[Xs]=i}catch(x){ve(e,e.return,x)}}break;case 6:if(St(t,e),Dt(e),r&4){if(e.stateNode===null)throw Error(V(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(x){ve(e,e.return,x)}}break;case 3:if(St(t,e),Dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ws(t.containerInfo)}catch(x){ve(e,e.return,x)}break;case 4:St(t,e),Dt(e);break;case 13:St(t,e),Dt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Pc=Se())),r&4&&Xf(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Ie=(c=Ie)||d,St(t,e),Ie=c):St(t,e),Dt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(B=e,d=e.child;d!==null;){for(h=B=d;B!==null;){switch(f=B,g=f.child,f.tag){case 0:case 11:case 14:case 15:As(4,f,f.return);break;case 1:Nr(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){ve(r,n,x)}}break;case 5:Nr(f,f.return);break;case 22:if(f.memoizedState!==null){Zf(h);continue}}g!==null?(g.return=f,B=g):Zf(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,c?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=h.stateNode,u=h.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Cp("display",o))}catch(x){ve(e,e.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(x){ve(e,e.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:St(t,e),Dt(e),r&4&&Xf(e);break;case 21:break;default:St(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Zg(n)){var r=n;break e}n=n.return}throw Error(V(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&($s(s,""),r.flags&=-33);var i=Qf(e);mu(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Qf(e);hu(e,a,o);break;default:throw Error(V(161))}}catch(u){ve(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Y1(e,t,n){B=e,t0(e)}function t0(e,t,n){for(var r=(e.mode&1)!==0;B!==null;){var s=B,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Ii;if(!o){var a=s.alternate,u=a!==null&&a.memoizedState!==null||Ie;a=Ii;var c=Ie;if(Ii=o,(Ie=u)&&!c)for(B=s;B!==null;)o=B,u=o.child,o.tag===22&&o.memoizedState!==null?Jf(s):u!==null?(u.return=o,B=u):Jf(s);for(;i!==null;)B=i,t0(i),i=i.sibling;B=s,Ii=a,Ie=c}Yf(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,B=i):Yf(e)}}function Yf(e){for(;B!==null;){var t=B;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ie||la(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ie)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:kt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Lf(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Lf(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Ws(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(V(163))}Ie||t.flags&512&&fu(t)}catch(f){ve(t,t.return,f)}}if(t===e){B=null;break}if(n=t.sibling,n!==null){n.return=t.return,B=n;break}B=t.return}}function Zf(e){for(;B!==null;){var t=B;if(t===e){B=null;break}var n=t.sibling;if(n!==null){n.return=t.return,B=n;break}B=t.return}}function Jf(e){for(;B!==null;){var t=B;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{la(4,t)}catch(u){ve(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){ve(t,s,u)}}var i=t.return;try{fu(t)}catch(u){ve(t,i,u)}break;case 5:var o=t.return;try{fu(t)}catch(u){ve(t,o,u)}}}catch(u){ve(t,t.return,u)}if(t===e){B=null;break}var a=t.sibling;if(a!==null){a.return=t.return,B=a;break}B=t.return}}var Z1=Math.ceil,Do=on.ReactCurrentDispatcher,_c=on.ReactCurrentOwner,yt=on.ReactCurrentBatchConfig,ee=0,Pe=null,je=null,Me=0,et=0,_r=Dn(0),_e=0,ni=null,or=0,ua=0,Ec=0,Ds=null,Qe=null,Pc=0,Kr=1/0,Ht=null,Mo=!1,pu=null,kn=null,Oi=!1,yn=null,Ro=0,Ms=0,gu=null,so=-1,io=0;function We(){return ee&6?Se():so!==-1?so:so=Se()}function Tn(e){return e.mode&1?ee&2&&Me!==0?Me&-Me:L1.transition!==null?(io===0&&(io=Op()),io):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Kp(e.type)),e):1}function Pt(e,t,n,r){if(50<Ms)throw Ms=0,gu=null,Error(V(185));mi(e,n,r),(!(ee&2)||e!==Pe)&&(e===Pe&&(!(ee&2)&&(ua|=n),_e===4&&pn(e,Me)),Je(e,r),n===1&&ee===0&&!(t.mode&1)&&(Kr=Se()+500,ia&&Mn()))}function Je(e,t){var n=e.callbackNode;Lx(e,t);var r=yo(e,e===Pe?Me:0);if(r===0)n!==null&&lf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lf(n),t===1)e.tag===0?R1(eh.bind(null,e)):dg(eh.bind(null,e)),P1(function(){!(ee&6)&&Mn()}),n=null;else{switch(zp(r)){case 1:n=nc;break;case 4:n=Fp;break;case 16:n=go;break;case 536870912:n=Ip;break;default:n=go}n=u0(n,n0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function n0(e,t){if(so=-1,io=0,ee&6)throw Error(V(327));var n=e.callbackNode;if(Or()&&e.callbackNode!==n)return null;var r=yo(e,e===Pe?Me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Lo(e,r);else{t=r;var s=ee;ee|=2;var i=s0();(Pe!==e||Me!==t)&&(Ht=null,Kr=Se()+500,Zn(e,t));do try{tw();break}catch(a){r0(e,a)}while(!0);pc(),Do.current=i,ee=s,je!==null?t=0:(Pe=null,Me=0,t=_e)}if(t!==0){if(t===2&&(s=Bl(e),s!==0&&(r=s,t=yu(e,s))),t===1)throw n=ni,Zn(e,0),pn(e,r),Je(e,Se()),n;if(t===6)pn(e,r);else{if(s=e.current.alternate,!(r&30)&&!J1(s)&&(t=Lo(e,r),t===2&&(i=Bl(e),i!==0&&(r=i,t=yu(e,i))),t===1))throw n=ni,Zn(e,0),pn(e,r),Je(e,Se()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(V(345));case 2:$n(e,Qe,Ht);break;case 3:if(pn(e,r),(r&130023424)===r&&(t=Pc+500-Se(),10<t)){if(yo(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){We(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Yl($n.bind(null,e,Qe,Ht),t);break}$n(e,Qe,Ht);break;case 4:if(pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Et(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Z1(r/1960))-r,10<r){e.timeoutHandle=Yl($n.bind(null,e,Qe,Ht),r);break}$n(e,Qe,Ht);break;case 5:$n(e,Qe,Ht);break;default:throw Error(V(329))}}}return Je(e,Se()),e.callbackNode===n?n0.bind(null,e):null}function yu(e,t){var n=Ds;return e.current.memoizedState.isDehydrated&&(Zn(e,t).flags|=256),e=Lo(e,t),e!==2&&(t=Qe,Qe=n,t!==null&&vu(t)),e}function vu(e){Qe===null?Qe=e:Qe.push.apply(Qe,e)}function J1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!At(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pn(e,t){for(t&=~Ec,t&=~ua,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Et(t),r=1<<n;e[n]=-1,t&=~r}}function eh(e){if(ee&6)throw Error(V(327));Or();var t=yo(e,0);if(!(t&1))return Je(e,Se()),null;var n=Lo(e,t);if(e.tag!==0&&n===2){var r=Bl(e);r!==0&&(t=r,n=yu(e,r))}if(n===1)throw n=ni,Zn(e,0),pn(e,t),Je(e,Se()),n;if(n===6)throw Error(V(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$n(e,Qe,Ht),Je(e,Se()),null}function Ac(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(Kr=Se()+500,ia&&Mn())}}function ar(e){yn!==null&&yn.tag===0&&!(ee&6)&&Or();var t=ee;ee|=1;var n=yt.transition,r=ne;try{if(yt.transition=null,ne=1,e)return e()}finally{ne=r,yt.transition=n,ee=t,!(ee&6)&&Mn()}}function Dc(){et=_r.current,le(_r)}function Zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,E1(n)),je!==null)for(n=je.return;n!==null;){var r=n;switch(fc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&bo();break;case 3:Hr(),le(Ye),le($e),Sc();break;case 5:wc(r);break;case 4:Hr();break;case 13:le(he);break;case 19:le(he);break;case 10:gc(r.type._context);break;case 22:case 23:Dc()}n=n.return}if(Pe=e,je=e=jn(e.current,null),Me=et=t,_e=0,ni=null,Ec=ua=or=0,Qe=Ds=null,qn!==null){for(t=0;t<qn.length;t++)if(n=qn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}qn=null}return e}function r0(e,t){do{var n=je;try{if(pc(),to.current=Ao,Po){for(var r=pe.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Po=!1}if(ir=0,Ee=Ne=pe=null,Ps=!1,Js=0,_c.current=null,n===null||n.return===null){_e=1,ni=t,je=null;break}e:{var i=e,o=n.return,a=n,u=t;if(t=Me,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=$f(o);if(g!==null){g.flags&=-257,Uf(g,o,a,i,t),g.mode&1&&zf(i,c,t),t=g,u=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(u),t.updateQueue=x}else v.add(u);break e}else{if(!(t&1)){zf(i,c,t),Mc();break e}u=Error(V(426))}}else if(de&&a.mode&1){var S=$f(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Uf(S,o,a,i,t),hc(Wr(u,a));break e}}i=u=Wr(u,a),_e!==4&&(_e=2),Ds===null?Ds=[i]:Ds.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=zg(i,u,t);Rf(i,p);break e;case 1:a=u;var m=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(kn===null||!kn.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var k=$g(i,a,t);Rf(i,k);break e}}i=i.return}while(i!==null)}o0(n)}catch(j){t=j,je===n&&n!==null&&(je=n=n.return);continue}break}while(!0)}function s0(){var e=Do.current;return Do.current=Ao,e===null?Ao:e}function Mc(){(_e===0||_e===3||_e===2)&&(_e=4),Pe===null||!(or&268435455)&&!(ua&268435455)||pn(Pe,Me)}function Lo(e,t){var n=ee;ee|=2;var r=s0();(Pe!==e||Me!==t)&&(Ht=null,Zn(e,t));do try{ew();break}catch(s){r0(e,s)}while(!0);if(pc(),ee=n,Do.current=r,je!==null)throw Error(V(261));return Pe=null,Me=0,_e}function ew(){for(;je!==null;)i0(je)}function tw(){for(;je!==null&&!Cx();)i0(je)}function i0(e){var t=l0(e.alternate,e,et);e.memoizedProps=e.pendingProps,t===null?o0(e):je=t,_c.current=null}function o0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=q1(n,t),n!==null){n.flags&=32767,je=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,je=null;return}}else if(n=G1(n,t,et),n!==null){je=n;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);_e===0&&(_e=5)}function $n(e,t,n){var r=ne,s=yt.transition;try{yt.transition=null,ne=1,nw(e,t,n,r)}finally{yt.transition=s,ne=r}return null}function nw(e,t,n,r){do Or();while(yn!==null);if(ee&6)throw Error(V(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(V(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Vx(e,i),e===Pe&&(je=Pe=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Oi||(Oi=!0,u0(go,function(){return Or(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=yt.transition,yt.transition=null;var o=ne;ne=1;var a=ee;ee|=4,_c.current=null,X1(e,n),e0(n,e),b1(Ql),vo=!!ql,Ql=ql=null,e.current=n,Y1(n),Nx(),ee=a,ne=o,yt.transition=i}else e.current=n;if(Oi&&(Oi=!1,yn=e,Ro=s),i=e.pendingLanes,i===0&&(kn=null),Px(n.stateNode),Je(e,Se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Mo)throw Mo=!1,e=pu,pu=null,e;return Ro&1&&e.tag!==0&&Or(),i=e.pendingLanes,i&1?e===gu?Ms++:(Ms=0,gu=e):Ms=0,Mn(),null}function Or(){if(yn!==null){var e=zp(Ro),t=yt.transition,n=ne;try{if(yt.transition=null,ne=16>e?16:e,yn===null)var r=!1;else{if(e=yn,yn=null,Ro=0,ee&6)throw Error(V(331));var s=ee;for(ee|=4,B=e.current;B!==null;){var i=B,o=i.child;if(B.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(B=c;B!==null;){var d=B;switch(d.tag){case 0:case 11:case 15:As(8,d,i)}var h=d.child;if(h!==null)h.return=d,B=h;else for(;B!==null;){d=B;var f=d.sibling,g=d.return;if(Yg(d),d===c){B=null;break}if(f!==null){f.return=g,B=f;break}B=g}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}B=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,B=o;else e:for(;B!==null;){if(i=B,i.flags&2048)switch(i.tag){case 0:case 11:case 15:As(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,B=p;break e}B=i.return}}var m=e.current;for(B=m;B!==null;){o=B;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,B=y;else e:for(o=m;B!==null;){if(a=B,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:la(9,a)}}catch(j){ve(a,a.return,j)}if(a===o){B=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,B=k;break e}B=a.return}}if(ee=s,Mn(),Vt&&typeof Vt.onPostCommitFiberRoot=="function")try{Vt.onPostCommitFiberRoot(ea,e)}catch{}r=!0}return r}finally{ne=n,yt.transition=t}}return!1}function th(e,t,n){t=Wr(n,t),t=zg(e,t,1),e=bn(e,t,1),t=We(),e!==null&&(mi(e,1,t),Je(e,t))}function ve(e,t,n){if(e.tag===3)th(e,e,n);else for(;t!==null;){if(t.tag===3){th(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(kn===null||!kn.has(r))){e=Wr(n,e),e=$g(t,e,1),t=bn(t,e,1),e=We(),t!==null&&(mi(t,1,e),Je(t,e));break}}t=t.return}}function rw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=We(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Me&n)===n&&(_e===4||_e===3&&(Me&130023424)===Me&&500>Se()-Pc?Zn(e,0):Ec|=n),Je(e,t)}function a0(e,t){t===0&&(e.mode&1?(t=Ei,Ei<<=1,!(Ei&130023424)&&(Ei=4194304)):t=1);var n=We();e=nn(e,t),e!==null&&(mi(e,t,n),Je(e,n))}function sw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),a0(e,n)}function iw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(V(314))}r!==null&&r.delete(t),a0(e,n)}var l0;l0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ye.current)Xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Xe=!1,K1(e,t,n);Xe=!!(e.flags&131072)}else Xe=!1,de&&t.flags&1048576&&fg(t,jo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ro(e,t),e=t.pendingProps;var s=$r(t,$e.current);Ir(t,n),s=kc(null,t,r,e,s,n);var i=Tc();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ze(r)?(i=!0,ko(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,vc(t),s.updater=aa,t.stateNode=s,s._reactInternals=t,su(t,r,e,n),t=au(null,t,r,!0,i,n)):(t.tag=0,de&&i&&dc(t),Ue(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ro(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=aw(r),e=kt(r,e),s){case 0:t=ou(null,t,r,e,n);break e;case 1:t=Wf(null,t,r,e,n);break e;case 11:t=Bf(null,t,r,e,n);break e;case 14:t=Hf(null,t,r,kt(r.type,e),n);break e}throw Error(V(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:kt(r,s),ou(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:kt(r,s),Wf(e,t,r,s,n);case 3:e:{if(Wg(t),e===null)throw Error(V(387));r=t.pendingProps,i=t.memoizedState,s=i.element,vg(e,t),_o(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Wr(Error(V(423)),t),t=Kf(e,t,r,n,s);break e}else if(r!==s){s=Wr(Error(V(424)),t),t=Kf(e,t,r,n,s);break e}else for(nt=Sn(t.stateNode.containerInfo.firstChild),rt=t,de=!0,jt=null,n=gg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ur(),r===s){t=rn(e,t,n);break e}Ue(e,t,r,n)}t=t.child}return t;case 5:return xg(t),e===null&&tu(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,Xl(r,s)?o=null:i!==null&&Xl(r,i)&&(t.flags|=32),Hg(e,t),Ue(e,t,o,n),t.child;case 6:return e===null&&tu(t),null;case 13:return Kg(e,t,n);case 4:return xc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Br(t,null,r,n):Ue(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:kt(r,s),Bf(e,t,r,s,n);case 7:return Ue(e,t,t.pendingProps,n),t.child;case 8:return Ue(e,t,t.pendingProps.children,n),t.child;case 12:return Ue(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,ie(Co,r._currentValue),r._currentValue=o,i!==null)if(At(i.value,o)){if(i.children===s.children&&!Ye.current){t=rn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Xt(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),nu(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(V(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),nu(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}Ue(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Ir(t,n),s=vt(s),r=r(s),t.flags|=1,Ue(e,t,r,n),t.child;case 14:return r=t.type,s=kt(r,t.pendingProps),s=kt(r.type,s),Hf(e,t,r,s,n);case 15:return Ug(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:kt(r,s),ro(e,t),t.tag=1,Ze(r)?(e=!0,ko(t)):e=!1,Ir(t,n),Og(t,r,s),su(t,r,s,n),au(null,t,r,!0,e,n);case 19:return Gg(e,t,n);case 22:return Bg(e,t,n)}throw Error(V(156,t.tag))};function u0(e,t){return Vp(e,t)}function ow(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,n,r){return new ow(e,t,n,r)}function Rc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function aw(e){if(typeof e=="function")return Rc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ju)return 11;if(e===ec)return 14}return 2}function jn(e,t){var n=e.alternate;return n===null?(n=gt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function oo(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")Rc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case vr:return Jn(n.children,s,i,t);case Zu:o=8,s|=8;break;case _l:return e=gt(12,n,t,s|2),e.elementType=_l,e.lanes=i,e;case El:return e=gt(13,n,t,s),e.elementType=El,e.lanes=i,e;case Pl:return e=gt(19,n,t,s),e.elementType=Pl,e.lanes=i,e;case xp:return ca(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case yp:o=10;break e;case vp:o=9;break e;case Ju:o=11;break e;case ec:o=14;break e;case fn:o=16,r=null;break e}throw Error(V(130,e==null?e:typeof e,""))}return t=gt(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function Jn(e,t,n,r){return e=gt(7,e,r,t),e.lanes=n,e}function ca(e,t,n,r){return e=gt(22,e,r,t),e.elementType=xp,e.lanes=n,e.stateNode={isHidden:!1},e}function nl(e,t,n){return e=gt(6,e,null,t),e.lanes=n,e}function rl(e,t,n){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function lw(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Fa(0),this.expirationTimes=Fa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fa(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Lc(e,t,n,r,s,i,o,a,u){return e=new lw(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=gt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},vc(i),e}function uw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:yr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function c0(e){if(!e)return Nn;e=e._reactInternals;e:{if(dr(e)!==e||e.tag!==1)throw Error(V(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ze(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(V(171))}if(e.tag===1){var n=e.type;if(Ze(n))return cg(e,n,t)}return t}function d0(e,t,n,r,s,i,o,a,u){return e=Lc(n,r,!0,e,s,i,o,a,u),e.context=c0(null),n=e.current,r=We(),s=Tn(n),i=Xt(r,s),i.callback=t??null,bn(n,i,s),e.current.lanes=s,mi(e,s,r),Je(e,r),e}function da(e,t,n,r){var s=t.current,i=We(),o=Tn(s);return n=c0(n),t.context===null?t.context=n:t.pendingContext=n,t=Xt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bn(s,t,o),e!==null&&(Pt(e,s,o,i),eo(e,s,o)),o}function Vo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Vc(e,t){nh(e,t),(e=e.alternate)&&nh(e,t)}function cw(){return null}var f0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fc(e){this._internalRoot=e}fa.prototype.render=Fc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(V(409));da(e,t,null,null)};fa.prototype.unmount=Fc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ar(function(){da(null,e,null,null)}),t[tn]=null}};function fa(e){this._internalRoot=e}fa.prototype.unstable_scheduleHydration=function(e){if(e){var t=Bp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<mn.length&&t!==0&&t<mn[n].priority;n++);mn.splice(n,0,e),n===0&&Wp(e)}};function Ic(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ha(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rh(){}function dw(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var c=Vo(o);i.call(c)}}var o=d0(t,r,e,0,null,!1,!1,"",rh);return e._reactRootContainer=o,e[tn]=o.current,qs(e.nodeType===8?e.parentNode:e),ar(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=Vo(u);a.call(c)}}var u=Lc(e,0,!1,null,null,!1,!1,"",rh);return e._reactRootContainer=u,e[tn]=u.current,qs(e.nodeType===8?e.parentNode:e),ar(function(){da(t,u,n,r)}),u}function ma(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var u=Vo(o);a.call(u)}}da(t,o,e,s)}else o=dw(n,t,e,s,r);return Vo(o)}$p=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=xs(t.pendingLanes);n!==0&&(rc(t,n|1),Je(t,Se()),!(ee&6)&&(Kr=Se()+500,Mn()))}break;case 13:ar(function(){var r=nn(e,1);if(r!==null){var s=We();Pt(r,e,1,s)}}),Vc(e,1)}};sc=function(e){if(e.tag===13){var t=nn(e,134217728);if(t!==null){var n=We();Pt(t,e,134217728,n)}Vc(e,134217728)}};Up=function(e){if(e.tag===13){var t=Tn(e),n=nn(e,t);if(n!==null){var r=We();Pt(n,e,t,r)}Vc(e,t)}};Bp=function(){return ne};Hp=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};zl=function(e,t,n){switch(t){case"input":if(Ml(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=sa(r);if(!s)throw Error(V(90));Sp(r),Ml(r,s)}}}break;case"textarea":kp(e,n);break;case"select":t=n.value,t!=null&&Rr(e,!!n.multiple,t,!1)}};Pp=Ac;Ap=ar;var fw={usingClientEntryPoint:!1,Events:[gi,br,sa,_p,Ep,Ac]},fs={findFiberByHostInstance:Gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},hw={bundleType:fs.bundleType,version:fs.version,rendererPackageName:fs.rendererPackageName,rendererConfig:fs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:on.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Rp(e),e===null?null:e.stateNode},findFiberByHostInstance:fs.findFiberByHostInstance||cw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zi.isDisabled&&zi.supportsFiber)try{ea=zi.inject(hw),Vt=zi}catch{}}ut.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fw;ut.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ic(t))throw Error(V(200));return uw(e,t,null,n)};ut.createRoot=function(e,t){if(!Ic(e))throw Error(V(299));var n=!1,r="",s=f0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Lc(e,1,!1,null,null,n,!1,r,s),e[tn]=t.current,qs(e.nodeType===8?e.parentNode:e),new Fc(t)};ut.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(V(188)):(e=Object.keys(e).join(","),Error(V(268,e)));return e=Rp(t),e=e===null?null:e.stateNode,e};ut.flushSync=function(e){return ar(e)};ut.hydrate=function(e,t,n){if(!ha(t))throw Error(V(200));return ma(null,e,t,!0,n)};ut.hydrateRoot=function(e,t,n){if(!Ic(e))throw Error(V(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=f0;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=d0(t,null,e,1,n??null,s,!1,i,o),e[tn]=t.current,qs(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new fa(t)};ut.render=function(e,t,n){if(!ha(t))throw Error(V(200));return ma(null,e,t,!1,n)};ut.unmountComponentAtNode=function(e){if(!ha(e))throw Error(V(40));return e._reactRootContainer?(ar(function(){ma(null,null,e,!1,function(){e._reactRootContainer=null,e[tn]=null})}),!0):!1};ut.unstable_batchedUpdates=Ac;ut.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ha(n))throw Error(V(200));if(e==null||e._reactInternals===void 0)throw Error(V(38));return ma(e,t,n,!1,r)};ut.version="18.3.1-next-f1338f8080-20240426";function h0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(h0)}catch(e){console.error(e)}}h0(),hp.exports=ut;var mw=hp.exports,sh=mw;Cl.createRoot=sh.createRoot,Cl.hydrateRoot=sh.hydrateRoot;const Oc=w.createContext({});function zc(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}const pa=w.createContext(null),$c=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class pw extends w.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function gw({children:e,isPresent:t}){const n=w.useId(),r=w.useRef(null),s=w.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=w.useContext($c);return w.useInsertionEffect(()=>{const{width:o,height:a,top:u,left:c}=s.current;if(t||!r.current||!o||!a)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return i&&(d.nonce=i),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${a}px !important;
            top: ${u}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),l.jsx(pw,{isPresent:t,childRef:r,sizeRef:s,children:w.cloneElement(e,{ref:r})})}const yw=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:i,mode:o})=>{const a=zc(vw),u=w.useId(),c=w.useCallback(h=>{a.set(h,!0);for(const f of a.values())if(!f)return;r&&r()},[a,r]),d=w.useMemo(()=>({id:u,initial:t,isPresent:n,custom:s,onExitComplete:c,register:h=>(a.set(h,!1),()=>a.delete(h))}),i?[Math.random(),c]:[n,c]);return w.useMemo(()=>{a.forEach((h,f)=>a.set(f,!1))},[n]),w.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=l.jsx(gw,{isPresent:n,children:e})),l.jsx(pa.Provider,{value:d,children:e})};function vw(){return new Map}function m0(e=!0){const t=w.useContext(pa);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,i=w.useId();w.useEffect(()=>{e&&s(i)},[e]);const o=w.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,o]:[!0]}const $i=e=>e.key||"";function ih(e){const t=[];return w.Children.forEach(e,n=>{w.isValidElement(n)&&t.push(n)}),t}const Uc=typeof window<"u",p0=Uc?w.useLayoutEffect:w.useEffect,ae=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:i="sync",propagate:o=!1})=>{const[a,u]=m0(o),c=w.useMemo(()=>ih(e),[e]),d=o&&!a?[]:c.map($i),h=w.useRef(!0),f=w.useRef(c),g=zc(()=>new Map),[v,x]=w.useState(c),[S,p]=w.useState(c);p0(()=>{h.current=!1,f.current=c;for(let k=0;k<S.length;k++){const j=$i(S[k]);d.includes(j)?g.delete(j):g.get(j)!==!0&&g.set(j,!1)}},[S,d.length,d.join("-")]);const m=[];if(c!==v){let k=[...c];for(let j=0;j<S.length;j++){const C=S[j],_=$i(C);d.includes(_)||(k.splice(j,0,C),m.push(C))}i==="wait"&&m.length&&(k=m),p(ih(k)),x(c);return}const{forceRender:y}=w.useContext(Oc);return l.jsx(l.Fragment,{children:S.map(k=>{const j=$i(k),C=o&&!a?!1:c===S||d.includes(j),_=()=>{if(g.has(j))g.set(j,!0);else return;let T=!0;g.forEach(E=>{E||(T=!1)}),T&&(y==null||y(),p(f.current),o&&(u==null||u()),r&&r())};return l.jsx(yw,{isPresent:C,initial:!h.current||n?void 0:!1,custom:C?void 0:t,presenceAffectsLayout:s,mode:i,onExitComplete:C?void 0:_,children:k},j)})})},st=e=>e;let g0=st;function Bc(e){let t;return()=>(t===void 0&&(t=e()),t)}const Gr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Yt=e=>e*1e3,Zt=e=>e/1e3,xw={useManualTiming:!1};function ww(e){let t=new Set,n=new Set,r=!1,s=!1;const i=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(c){i.has(c)&&(u.schedule(c),e()),c(o)}const u={schedule:(c,d=!1,h=!1)=>{const g=h&&r?t:n;return d&&i.add(c),g.has(c)||g.add(c),c},cancel:c=>{n.delete(c),i.delete(c)},process:c=>{if(o=c,r){s=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,s&&(s=!1,u.process(c))}};return u}const Ui=["read","resolveKeyframes","update","preRender","render","postRender"],Sw=40;function y0(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,o=Ui.reduce((p,m)=>(p[m]=ww(i),p),{}),{read:a,resolveKeyframes:u,update:c,preRender:d,render:h,postRender:f}=o,g=()=>{const p=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(p-s.timestamp,Sw),1),s.timestamp=p,s.isProcessing=!0,a.process(s),u.process(s),c.process(s),d.process(s),h.process(s),f.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:Ui.reduce((p,m)=>{const y=o[m];return p[m]=(k,j=!1,C=!1)=>(n||v(),y.schedule(k,j,C)),p},{}),cancel:p=>{for(let m=0;m<Ui.length;m++)o[Ui[m]].cancel(p)},state:s,steps:o}}const{schedule:ue,cancel:_n,state:De,steps:sl}=y0(typeof requestAnimationFrame<"u"?requestAnimationFrame:st,!0),v0=w.createContext({strict:!1}),oh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},qr={};for(const e in oh)qr[e]={isEnabled:t=>oh[e].some(n=>!!t[n])};function bw(e){for(const t in e)qr[t]={...qr[t],...e[t]}}const kw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||kw.has(e)}let x0=e=>!Fo(e);function Tw(e){e&&(x0=t=>t.startsWith("on")?!Fo(t):e(t))}try{Tw(require("@emotion/is-prop-valid").default)}catch{}function jw(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(x0(s)||n===!0&&Fo(s)||!t&&!Fo(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function Cw(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const ga=w.createContext({});function ri(e){return typeof e=="string"||Array.isArray(e)}function ya(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Hc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Wc=["initial",...Hc];function va(e){return ya(e.animate)||Wc.some(t=>ri(e[t]))}function w0(e){return!!(va(e)||e.variants)}function Nw(e,t){if(va(e)){const{initial:n,animate:r}=e;return{initial:n===!1||ri(n)?n:void 0,animate:ri(r)?r:void 0}}return e.inherit!==!1?t:{}}function _w(e){const{initial:t,animate:n}=Nw(e,w.useContext(ga));return w.useMemo(()=>({initial:t,animate:n}),[ah(t),ah(n)])}function ah(e){return Array.isArray(e)?e.join(" "):e}const Ew=Symbol.for("motionComponentSymbol");function Er(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Pw(e,t,n){return w.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Er(n)&&(n.current=r))},[t])}const Kc=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Aw="framerAppearId",S0="data-"+Kc(Aw),{schedule:Gc}=y0(queueMicrotask,!1),b0=w.createContext({});function Dw(e,t,n,r,s){var i,o;const{visualElement:a}=w.useContext(ga),u=w.useContext(v0),c=w.useContext(pa),d=w.useContext($c).reducedMotion,h=w.useRef(null);r=r||u.renderer,!h.current&&r&&(h.current=r(e,{visualState:t,parent:a,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:d}));const f=h.current,g=w.useContext(b0);f&&!f.projection&&s&&(f.type==="html"||f.type==="svg")&&Mw(h.current,n,s,g);const v=w.useRef(!1);w.useInsertionEffect(()=>{f&&v.current&&f.update(n,c)});const x=n[S0],S=w.useRef(!!x&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return p0(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Gc.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),w.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,x)}),S.current=!1))}),f}function Mw(e,t,n,r){const{layoutId:s,layout:i,drag:o,dragConstraints:a,layoutScroll:u,layoutRoot:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:k0(e.parent)),e.projection.setOptions({layoutId:s,layout:i,alwaysMeasureLayout:!!o||a&&Er(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:u,layoutRoot:c})}function k0(e){if(e)return e.options.allowProjection!==!1?e.projection:k0(e.parent)}function Rw({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){var i,o;e&&bw(e);function a(c,d){let h;const f={...w.useContext($c),...c,layoutId:Lw(c)},{isStatic:g}=f,v=_w(c),x=r(c,g);if(!g&&Uc){Vw();const S=Fw(f);h=S.MeasureLayout,v.visualElement=Dw(s,x,f,t,S.ProjectionNode)}return l.jsxs(ga.Provider,{value:v,children:[h&&v.visualElement?l.jsx(h,{visualElement:v.visualElement,...f}):null,n(s,c,Pw(x,v.visualElement,d),x,g,v.visualElement)]})}a.displayName=`motion.${typeof s=="string"?s:`create(${(o=(i=s.displayName)!==null&&i!==void 0?i:s.name)!==null&&o!==void 0?o:""})`}`;const u=w.forwardRef(a);return u[Ew]=s,u}function Lw({layoutId:e}){const t=w.useContext(Oc).id;return t&&e!==void 0?t+"-"+e:e}function Vw(e,t){w.useContext(v0).strict}function Fw(e){const{drag:t,layout:n}=qr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Iw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function qc(e){return typeof e!="string"||e.includes("-")?!1:!!(Iw.indexOf(e)>-1||/[A-Z]/u.test(e))}function lh(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Qc(e,t,n,r){if(typeof t=="function"){const[s,i]=lh(r);t=t(n!==void 0?n:e.custom,s,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,i]=lh(r);t=t(n!==void 0?n:e.custom,s,i)}return t}const xu=e=>Array.isArray(e),Ow=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),zw=e=>xu(e)?e[e.length-1]||0:e,ze=e=>!!(e&&e.getVelocity);function ao(e){const t=ze(e)?e.get():e;return Ow(t)?t.toValue():t}function $w({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,s,i){const o={latestValues:Uw(r,s,i,e),renderState:t()};return n&&(o.onMount=a=>n({props:r,current:a,...o}),o.onUpdate=a=>n(a)),o}const T0=e=>(t,n)=>{const r=w.useContext(ga),s=w.useContext(pa),i=()=>$w(e,t,r,s);return n?i():zc(i)};function Uw(e,t,n,r){const s={},i=r(e,{});for(const f in i)s[f]=ao(i[f]);let{initial:o,animate:a}=e;const u=va(e),c=w0(e);t&&c&&!u&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let d=n?n.initial===!1:!1;d=d||o===!1;const h=d?a:o;if(h&&typeof h!="boolean"&&!ya(h)){const f=Array.isArray(h)?h:[h];for(let g=0;g<f.length;g++){const v=Qc(e,f[g]);if(v){const{transitionEnd:x,transition:S,...p}=v;for(const m in p){let y=p[m];if(Array.isArray(y)){const k=d?y.length-1:0;y=y[k]}y!==null&&(s[m]=y)}for(const m in x)s[m]=x[m]}}}return s}const ns=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],fr=new Set(ns),j0=e=>t=>typeof t=="string"&&t.startsWith(e),C0=j0("--"),Bw=j0("var(--"),Xc=e=>Bw(e)?Hw.test(e.split("/*")[0].trim()):!1,Hw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,N0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,sn=(e,t,n)=>n>t?t:n<e?e:n,rs={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},si={...rs,transform:e=>sn(0,1,e)},Bi={...rs,default:1},vi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),dn=vi("deg"),It=vi("%"),Q=vi("px"),Ww=vi("vh"),Kw=vi("vw"),uh={...It,parse:e=>It.parse(e)/100,transform:e=>It.transform(e*100)},Gw={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q},qw={rotate:dn,rotateX:dn,rotateY:dn,rotateZ:dn,scale:Bi,scaleX:Bi,scaleY:Bi,scaleZ:Bi,skew:dn,skewX:dn,skewY:dn,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:si,originX:uh,originY:uh,originZ:Q},ch={...rs,transform:Math.round},Yc={...Gw,...qw,zIndex:ch,size:Q,fillOpacity:si,strokeOpacity:si,numOctaves:ch},Qw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Xw=ns.length;function Yw(e,t,n){let r="",s=!0;for(let i=0;i<Xw;i++){const o=ns[i],a=e[o];if(a===void 0)continue;let u=!0;if(typeof a=="number"?u=a===(o.startsWith("scale")?1:0):u=parseFloat(a)===0,!u||n){const c=N0(a,Yc[o]);if(!u){s=!1;const d=Qw[o]||o;r+=`${d}(${c}) `}n&&(t[o]=c)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function Zc(e,t,n){const{style:r,vars:s,transformOrigin:i}=e;let o=!1,a=!1;for(const u in t){const c=t[u];if(fr.has(u)){o=!0;continue}else if(C0(u)){s[u]=c;continue}else{const d=N0(c,Yc[u]);u.startsWith("origin")?(a=!0,i[u]=d):r[u]=d}}if(t.transform||(o||n?r.transform=Yw(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:u="50%",originY:c="50%",originZ:d=0}=i;r.transformOrigin=`${u} ${c} ${d}`}}const Zw={offset:"stroke-dashoffset",array:"stroke-dasharray"},Jw={offset:"strokeDashoffset",array:"strokeDasharray"};function eS(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?Zw:Jw;e[i.offset]=Q.transform(-r);const o=Q.transform(t),a=Q.transform(n);e[i.array]=`${o} ${a}`}function dh(e,t,n){return typeof e=="string"?e:Q.transform(t+n*e)}function tS(e,t,n){const r=dh(t,e.x,e.width),s=dh(n,e.y,e.height);return`${r} ${s}`}function Jc(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:u=0,...c},d,h){if(Zc(e,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:g,dimensions:v}=e;f.transform&&(v&&(g.transform=f.transform),delete f.transform),v&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=tS(v,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&eS(f,o,a,u,!1)}const ed=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),_0=()=>({...ed(),attrs:{}}),td=e=>typeof e=="string"&&e.toLowerCase()==="svg";function E0(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const P0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function A0(e,t,n,r){E0(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(P0.has(s)?s:Kc(s),t.attrs[s])}const Io={};function nS(e){Object.assign(Io,e)}function D0(e,{layout:t,layoutId:n}){return fr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Io[e]||e==="opacity")}function nd(e,t,n){var r;const{style:s}=e,i={};for(const o in s)(ze(s[o])||t.style&&ze(t.style[o])||D0(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[o]=s[o]);return i}function M0(e,t,n){const r=nd(e,t,n);for(const s in e)if(ze(e[s])||ze(t[s])){const i=ns.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[i]=e[s]}return r}function rS(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const fh=["x","y","width","height","cx","cy","r"],sS={useVisualState:T0({scrapeMotionValuesFromProps:M0,createRenderState:_0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:s})=>{if(!n)return;let i=!!e.drag;if(!i){for(const a in s)if(fr.has(a)){i=!0;break}}if(!i)return;let o=!t;if(t)for(let a=0;a<fh.length;a++){const u=fh[a];e[u]!==t[u]&&(o=!0)}o&&ue.read(()=>{rS(n,r),ue.render(()=>{Jc(r,s,td(n.tagName),e.transformTemplate),A0(n,r)})})}})},iS={useVisualState:T0({scrapeMotionValuesFromProps:nd,createRenderState:ed})};function R0(e,t,n){for(const r in t)!ze(t[r])&&!D0(r,n)&&(e[r]=t[r])}function oS({transformTemplate:e},t){return w.useMemo(()=>{const n=ed();return Zc(n,t,e),Object.assign({},n.vars,n.style)},[t])}function aS(e,t){const n=e.style||{},r={};return R0(r,n,e),Object.assign(r,oS(e,t)),r}function lS(e,t){const n={},r=aS(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function uS(e,t,n,r){const s=w.useMemo(()=>{const i=_0();return Jc(i,t,td(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};R0(i,e.style,e),s.style={...i,...s.style}}return s}function cS(e=!1){return(n,r,s,{latestValues:i},o)=>{const u=(qc(n)?uS:lS)(r,i,o,n),c=jw(r,typeof n=="string",e),d=n!==w.Fragment?{...c,...u,ref:s}:{},{children:h}=r,f=w.useMemo(()=>ze(h)?h.get():h,[h]);return w.createElement(n,{...d,children:f})}}function dS(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...qc(r)?sS:iS,preloadedFeatures:e,useRender:cS(s),createVisualElement:t,Component:r};return Rw(o)}}function L0(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function xa(e,t,n){const r=e.getProps();return Qc(r,t,n!==void 0?n:r.custom,e)}const fS=Bc(()=>window.ScrollTimeline!==void 0);class hS{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(s=>{if(fS()&&s.attachTimeline)return s.attachTimeline(t);if(typeof n=="function")return n(s)});return()=>{r.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class mS extends hS{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function rd(e,t){return e?e[t]||e.default||e:void 0}const wu=2e4;function V0(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<wu;)t+=n,r=e.next(t);return t>=wu?1/0:t}function sd(e){return typeof e=="function"}function hh(e,t){e.timeline=t,e.onfinish=null}const id=e=>Array.isArray(e)&&typeof e[0]=="number",pS={linearEasing:void 0};function gS(e,t){const n=Bc(e);return()=>{var r;return(r=pS[t])!==null&&r!==void 0?r:n()}}const Oo=gS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),F0=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let i=0;i<s;i++)r+=e(Gr(0,s-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function I0(e){return!!(typeof e=="function"&&Oo()||!e||typeof e=="string"&&(e in Su||Oo())||id(e)||Array.isArray(e)&&e.every(I0))}const Ss=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Su={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ss([0,.65,.55,1]),circOut:Ss([.55,0,1,.45]),backIn:Ss([.31,.01,.66,-.59]),backOut:Ss([.33,1.53,.69,.99])};function O0(e,t){if(e)return typeof e=="function"&&Oo()?F0(e,t):id(e)?Ss(e):Array.isArray(e)?e.map(n=>O0(n,t)||Su.easeOut):Su[e]}const bt={x:!1,y:!1};function z0(){return bt.x||bt.y}function yS(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let s=document;const i=(r=void 0)!==null&&r!==void 0?r:s.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function $0(e,t){const n=yS(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function mh(e){return t=>{t.pointerType==="touch"||z0()||e(t)}}function vS(e,t,n={}){const[r,s,i]=$0(e,n),o=mh(a=>{const{target:u}=a,c=t(a);if(typeof c!="function"||!u)return;const d=mh(h=>{c(h),u.removeEventListener("pointerleave",d)});u.addEventListener("pointerleave",d,s)});return r.forEach(a=>{a.addEventListener("pointerenter",o,s)}),i}const U0=(e,t)=>t?e===t?!0:U0(e,t.parentElement):!1,od=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,xS=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function wS(e){return xS.has(e.tagName)||e.tabIndex!==-1}const bs=new WeakSet;function ph(e){return t=>{t.key==="Enter"&&e(t)}}function il(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const SS=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=ph(()=>{if(bs.has(n))return;il(n,"down");const s=ph(()=>{il(n,"up")}),i=()=>il(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function gh(e){return od(e)&&!z0()}function bS(e,t,n={}){const[r,s,i]=$0(e,n),o=a=>{const u=a.currentTarget;if(!gh(a)||bs.has(u))return;bs.add(u);const c=t(a),d=(g,v)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!gh(g)||!bs.has(u))&&(bs.delete(u),typeof c=="function"&&c(g,{success:v}))},h=g=>{d(g,n.useGlobalTarget||U0(u,g.target))},f=g=>{d(g,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",f,s)};return r.forEach(a=>{!wS(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,s),a.addEventListener("focus",c=>SS(c,s),s)}),i}function kS(e){return e==="x"||e==="y"?bt[e]?null:(bt[e]=!0,()=>{bt[e]=!1}):bt.x||bt.y?null:(bt.x=bt.y=!0,()=>{bt.x=bt.y=!1})}const B0=new Set(["width","height","top","left","right","bottom",...ns]);let lo;function TS(){lo=void 0}const Ot={now:()=>(lo===void 0&&Ot.set(De.isProcessing||xw.useManualTiming?De.timestamp:performance.now()),lo),set:e=>{lo=e,queueMicrotask(TS)}};function ad(e,t){e.indexOf(t)===-1&&e.push(t)}function ld(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ud{constructor(){this.subscriptions=[]}add(t){return ad(this.subscriptions,t),()=>ld(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const o=this.subscriptions[i];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function H0(e,t){return t?e*(1e3/t):0}const yh=30,jS=e=>!isNaN(parseFloat(e));class CS{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,s=!0)=>{const i=Ot.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Ot.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=jS(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ud);const r=this.events[t].add(n);return t==="change"?()=>{r(),ue.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Ot.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>yh)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,yh);return H0(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ii(e,t){return new CS(e,t)}function NS(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ii(n))}function _S(e,t){const n=xa(e,t);let{transitionEnd:r={},transition:s={},...i}=n||{};i={...i,...r};for(const o in i){const a=zw(i[o]);NS(e,o,a)}}function ES(e){return!!(ze(e)&&e.add)}function bu(e,t){const n=e.getValue("willChange");if(ES(n))return n.add(t)}function W0(e){return e.props[S0]}const K0=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,PS=1e-7,AS=12;function DS(e,t,n,r,s){let i,o,a=0;do o=t+(n-t)/2,i=K0(o,r,s)-e,i>0?n=o:t=o;while(Math.abs(i)>PS&&++a<AS);return o}function xi(e,t,n,r){if(e===t&&n===r)return st;const s=i=>DS(i,0,1,e,n);return i=>i===0||i===1?i:K0(s(i),t,r)}const G0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,q0=e=>t=>1-e(1-t),Q0=xi(.33,1.53,.69,.99),cd=q0(Q0),X0=G0(cd),Y0=e=>(e*=2)<1?.5*cd(e):.5*(2-Math.pow(2,-10*(e-1))),dd=e=>1-Math.sin(Math.acos(e)),Z0=q0(dd),J0=G0(dd),ey=e=>/^0[^.\s]+$/u.test(e);function MS(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||ey(e):!0}const Rs=e=>Math.round(e*1e5)/1e5,fd=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function RS(e){return e==null}const LS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,hd=(e,t)=>n=>!!(typeof n=="string"&&LS.test(n)&&n.startsWith(e)||t&&!RS(n)&&Object.prototype.hasOwnProperty.call(n,t)),ty=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,i,o,a]=r.match(fd);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},VS=e=>sn(0,255,e),ol={...rs,transform:e=>Math.round(VS(e))},Xn={test:hd("rgb","red"),parse:ty("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ol.transform(e)+", "+ol.transform(t)+", "+ol.transform(n)+", "+Rs(si.transform(r))+")"};function FS(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const ku={test:hd("#"),parse:FS,transform:Xn.transform},Pr={test:hd("hsl","hue"),parse:ty("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+It.transform(Rs(t))+", "+It.transform(Rs(n))+", "+Rs(si.transform(r))+")"},Fe={test:e=>Xn.test(e)||ku.test(e)||Pr.test(e),parse:e=>Xn.test(e)?Xn.parse(e):Pr.test(e)?Pr.parse(e):ku.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Xn.transform(e):Pr.transform(e)},IS=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function OS(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(fd))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(IS))===null||n===void 0?void 0:n.length)||0)>0}const ny="number",ry="color",zS="var",$S="var(",vh="${}",US=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function oi(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let i=0;const a=t.replace(US,u=>(Fe.test(u)?(r.color.push(i),s.push(ry),n.push(Fe.parse(u))):u.startsWith($S)?(r.var.push(i),s.push(zS),n.push(u)):(r.number.push(i),s.push(ny),n.push(parseFloat(u))),++i,vh)).split(vh);return{values:n,split:a,indexes:r,types:s}}function sy(e){return oi(e).values}function iy(e){const{split:t,types:n}=oi(e),r=t.length;return s=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],s[o]!==void 0){const a=n[o];a===ny?i+=Rs(s[o]):a===ry?i+=Fe.transform(s[o]):i+=s[o]}return i}}const BS=e=>typeof e=="number"?0:e;function HS(e){const t=sy(e);return iy(e)(t.map(BS))}const En={test:OS,parse:sy,createTransformer:iy,getAnimatableNone:HS},WS=new Set(["brightness","contrast","saturate","opacity"]);function KS(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(fd)||[];if(!r)return e;const s=n.replace(r,"");let i=WS.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const GS=/\b([a-z-]*)\(.*?\)/gu,Tu={...En,getAnimatableNone:e=>{const t=e.match(GS);return t?t.map(KS).join(" "):e}},qS={...Yc,color:Fe,backgroundColor:Fe,outlineColor:Fe,fill:Fe,stroke:Fe,borderColor:Fe,borderTopColor:Fe,borderRightColor:Fe,borderBottomColor:Fe,borderLeftColor:Fe,filter:Tu,WebkitFilter:Tu},md=e=>qS[e];function oy(e,t){let n=md(e);return n!==Tu&&(n=En),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const QS=new Set(["auto","none","0"]);function XS(e,t,n){let r=0,s;for(;r<e.length&&!s;){const i=e[r];typeof i=="string"&&!QS.has(i)&&oi(i).values.length&&(s=e[r]),r++}if(s&&n)for(const i of t)e[i]=oy(n,s)}const xh=e=>e===rs||e===Q,wh=(e,t)=>parseFloat(e.split(", ")[t]),Sh=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/u);if(s)return wh(s[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?wh(i[1],e):0}},YS=new Set(["x","y","z"]),ZS=ns.filter(e=>!YS.has(e));function JS(e){const t=[];return ZS.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Qr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Sh(4,13),y:Sh(5,14)};Qr.translateX=Qr.x;Qr.translateY=Qr.y;const er=new Set;let ju=!1,Cu=!1;function ay(){if(Cu){const e=Array.from(er).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=JS(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([i,o])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Cu=!1,ju=!1,er.forEach(e=>e.complete()),er.clear()}function ly(){er.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Cu=!0)})}function e2(){ly(),ay()}class pd{constructor(t,n,r,s,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(er.add(this),ju||(ju=!0,ue.read(ly),ue.resolveKeyframes(ay))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const o=s==null?void 0:s.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const u=r.readValue(n,a);u!=null&&(t[0]=u)}t[0]===void 0&&(t[0]=a),s&&o===void 0&&s.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),er.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,er.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const uy=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),t2=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function n2(e){const t=t2.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function cy(e,t,n=1){const[r,s]=n2(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const o=i.trim();return uy(o)?parseFloat(o):o}return Xc(s)?cy(s,t,n+1):s}const dy=e=>t=>t.test(e),r2={test:e=>e==="auto",parse:e=>e},fy=[rs,Q,It,dn,Kw,Ww,r2],bh=e=>fy.find(dy(e));class hy extends pd{constructor(t,n,r,s,i){super(t,n,r,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let u=0;u<t.length;u++){let c=t[u];if(typeof c=="string"&&(c=c.trim(),Xc(c))){const d=cy(c,n.current);d!==void 0&&(t[u]=d),u===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!B0.has(r)||t.length!==2)return;const[s,i]=t,o=bh(s),a=bh(i);if(o!==a)if(xh(o)&&xh(a))for(let u=0;u<t.length;u++){const c=t[u];typeof c=="string"&&(t[u]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)MS(t[s])&&r.push(s);r.length&&XS(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Qr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:s}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,a=s[o];s[o]=Qr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([u,c])=>{n.getValue(u).set(c)}),this.resolveNoneKeyframes()}}const kh=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(En.test(e)||e==="0")&&!e.startsWith("url("));function s2(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function i2(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],o=kh(s,t),a=kh(i,t);return!o||!a?!1:s2(e)||(n==="spring"||sd(n))&&r}const o2=e=>e!==null;function wa(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(o2),i=t&&n!=="loop"&&t%2===1?0:s.length-1;return!i||r===void 0?s[i]:r}const a2=40;class my{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Ot.now(),this.options={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>a2?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&e2(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Ot.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:i,delay:o,onComplete:a,onUpdate:u,isGenerator:c}=this.options;if(!c&&!i2(t,r,s,i))if(o)this.options.duration=0;else{u&&u(wa(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const me=(e,t,n)=>e+(t-e)*n;function al(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function l2({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,o=0;if(!t)s=i=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,u=2*n-a;s=al(u,a,e+1/3),i=al(u,a,e),o=al(u,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}function zo(e,t){return n=>n>0?t:e}const ll=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},u2=[ku,Xn,Pr],c2=e=>u2.find(t=>t.test(e));function Th(e){const t=c2(e);if(!t)return!1;let n=t.parse(e);return t===Pr&&(n=l2(n)),n}const jh=(e,t)=>{const n=Th(e),r=Th(t);if(!n||!r)return zo(e,t);const s={...n};return i=>(s.red=ll(n.red,r.red,i),s.green=ll(n.green,r.green,i),s.blue=ll(n.blue,r.blue,i),s.alpha=me(n.alpha,r.alpha,i),Xn.transform(s))},d2=(e,t)=>n=>t(e(n)),wi=(...e)=>e.reduce(d2),Nu=new Set(["none","hidden"]);function f2(e,t){return Nu.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function h2(e,t){return n=>me(e,t,n)}function gd(e){return typeof e=="number"?h2:typeof e=="string"?Xc(e)?zo:Fe.test(e)?jh:g2:Array.isArray(e)?py:typeof e=="object"?Fe.test(e)?jh:m2:zo}function py(e,t){const n=[...e],r=n.length,s=e.map((i,o)=>gd(i)(i,t[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}}function m2(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=gd(e[s])(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}}function p2(e,t){var n;const r=[],s={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const o=t.types[i],a=e.indexes[o][s[o]],u=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=u,s[o]++}return r}const g2=(e,t)=>{const n=En.createTransformer(t),r=oi(e),s=oi(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?Nu.has(e)&&!s.values.length||Nu.has(t)&&!r.values.length?f2(e,t):wi(py(p2(r,s),s.values),n):zo(e,t)};function gy(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?me(e,t,n):gd(e)(e,t)}const y2=5;function yy(e,t,n){const r=Math.max(t-y2,0);return H0(n-e(r),t-r)}const ye={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ul=.001;function v2({duration:e=ye.duration,bounce:t=ye.bounce,velocity:n=ye.velocity,mass:r=ye.mass}){let s,i,o=1-t;o=sn(ye.minDamping,ye.maxDamping,o),e=sn(ye.minDuration,ye.maxDuration,Zt(e)),o<1?(s=c=>{const d=c*o,h=d*e,f=d-n,g=_u(c,o),v=Math.exp(-h);return ul-f/g*v},i=c=>{const h=c*o*e,f=h*n+n,g=Math.pow(o,2)*Math.pow(c,2)*e,v=Math.exp(-h),x=_u(Math.pow(c,2),o);return(-s(c)+ul>0?-1:1)*((f-g)*v)/x}):(s=c=>{const d=Math.exp(-c*e),h=(c-n)*e+1;return-ul+d*h},i=c=>{const d=Math.exp(-c*e),h=(n-c)*(e*e);return d*h});const a=5/e,u=w2(s,i,a);if(e=Yt(e),isNaN(u))return{stiffness:ye.stiffness,damping:ye.damping,duration:e};{const c=Math.pow(u,2)*r;return{stiffness:c,damping:o*2*Math.sqrt(r*c),duration:e}}}const x2=12;function w2(e,t,n){let r=n;for(let s=1;s<x2;s++)r=r-e(r)/t(r);return r}function _u(e,t){return e*Math.sqrt(1-t*t)}const S2=["duration","bounce"],b2=["stiffness","damping","mass"];function Ch(e,t){return t.some(n=>e[n]!==void 0)}function k2(e){let t={velocity:ye.velocity,stiffness:ye.stiffness,damping:ye.damping,mass:ye.mass,isResolvedFromDuration:!1,...e};if(!Ch(e,b2)&&Ch(e,S2))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,i=2*sn(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:ye.mass,stiffness:s,damping:i}}else{const n=v2(e);t={...t,...n,mass:ye.mass},t.isResolvedFromDuration=!0}return t}function vy(e=ye.visualDuration,t=ye.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const i=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:g}=k2({...n,velocity:-Zt(n.velocity||0)}),v=f||0,x=c/(2*Math.sqrt(u*d)),S=o-i,p=Zt(Math.sqrt(u/d)),m=Math.abs(S)<5;r||(r=m?ye.restSpeed.granular:ye.restSpeed.default),s||(s=m?ye.restDelta.granular:ye.restDelta.default);let y;if(x<1){const j=_u(p,x);y=C=>{const _=Math.exp(-x*p*C);return o-_*((v+x*p*S)/j*Math.sin(j*C)+S*Math.cos(j*C))}}else if(x===1)y=j=>o-Math.exp(-p*j)*(S+(v+p*S)*j);else{const j=p*Math.sqrt(x*x-1);y=C=>{const _=Math.exp(-x*p*C),T=Math.min(j*C,300);return o-_*((v+x*p*S)*Math.sinh(T)+j*S*Math.cosh(T))/j}}const k={calculatedDuration:g&&h||null,next:j=>{const C=y(j);if(g)a.done=j>=h;else{let _=0;x<1&&(_=j===0?Yt(v):yy(y,j,C));const T=Math.abs(_)<=r,E=Math.abs(o-C)<=s;a.done=T&&E}return a.value=a.done?o:C,a},toString:()=>{const j=Math.min(V0(k),wu),C=F0(_=>k.next(j*_).value,j,30);return j+"ms "+C}};return k}function Nh({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:u,restDelta:c=.5,restSpeed:d}){const h=e[0],f={done:!1,value:h},g=T=>a!==void 0&&T<a||u!==void 0&&T>u,v=T=>a===void 0?u:u===void 0||Math.abs(a-T)<Math.abs(u-T)?a:u;let x=n*t;const S=h+x,p=o===void 0?S:o(S);p!==S&&(x=p-h);const m=T=>-x*Math.exp(-T/r),y=T=>p+m(T),k=T=>{const E=m(T),A=y(T);f.done=Math.abs(E)<=c,f.value=f.done?p:A};let j,C;const _=T=>{g(f.value)&&(j=T,C=vy({keyframes:[f.value,v(f.value)],velocity:yy(y,T,f.value),damping:s,stiffness:i,restDelta:c,restSpeed:d}))};return _(0),{calculatedDuration:null,next:T=>{let E=!1;return!C&&j===void 0&&(E=!0,k(T),_(T)),j!==void 0&&T>=j?C.next(T-j):(!E&&k(T),f)}}}const T2=xi(.42,0,1,1),j2=xi(0,0,.58,1),xy=xi(.42,0,.58,1),C2=e=>Array.isArray(e)&&typeof e[0]!="number",N2={linear:st,easeIn:T2,easeInOut:xy,easeOut:j2,circIn:dd,circInOut:J0,circOut:Z0,backIn:cd,backInOut:X0,backOut:Q0,anticipate:Y0},_h=e=>{if(id(e)){g0(e.length===4);const[t,n,r,s]=e;return xi(t,n,r,s)}else if(typeof e=="string")return N2[e];return e};function _2(e,t,n){const r=[],s=n||gy,i=e.length-1;for(let o=0;o<i;o++){let a=s(e[o],e[o+1]);if(t){const u=Array.isArray(t)?t[o]||st:t;a=wi(u,a)}r.push(a)}return r}function E2(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(g0(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=_2(t,r,s),u=a.length,c=d=>{if(o&&d<e[0])return t[0];let h=0;if(u>1)for(;h<e.length-2&&!(d<e[h+1]);h++);const f=Gr(e[h],e[h+1],d);return a[h](f)};return n?d=>c(sn(e[0],e[i-1],d)):c}function P2(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=Gr(0,t,r);e.push(me(n,1,s))}}function A2(e){const t=[0];return P2(t,e.length-1),t}function D2(e,t){return e.map(n=>n*t)}function M2(e,t){return e.map(()=>t||xy).splice(0,e.length-1)}function $o({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=C2(r)?r.map(_h):_h(r),i={done:!1,value:t[0]},o=D2(n&&n.length===t.length?n:A2(t),e),a=E2(o,t,{ease:Array.isArray(s)?s:M2(t,s)});return{calculatedDuration:e,next:u=>(i.value=a(u),i.done=u>=e,i)}}const R2=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ue.update(t,!0),stop:()=>_n(t),now:()=>De.isProcessing?De.timestamp:Ot.now()}},L2={decay:Nh,inertia:Nh,tween:$o,keyframes:$o,spring:vy},V2=e=>e/100;class yd extends my{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:u}=this.options;u&&u()};const{name:n,motionValue:r,element:s,keyframes:i}=this.options,o=(s==null?void 0:s.KeyframeResolver)||pd,a=(u,c)=>this.onKeyframesResolved(u,c);this.resolver=new o(i,a,n,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:i,velocity:o=0}=this.options,a=sd(n)?n:L2[n]||$o;let u,c;a!==$o&&typeof t[0]!="number"&&(u=wi(V2,gy(t[0],t[1])),t=[0,100]);const d=a({...this.options,keyframes:t});i==="mirror"&&(c=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),d.calculatedDuration===null&&(d.calculatedDuration=V0(d));const{calculatedDuration:h}=d,f=h+s,g=f*(r+1)-s;return{generator:d,mirroredGenerator:c,mapPercentToKeyframes:u,calculatedDuration:h,resolvedDuration:f,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:T}=this.options;return{done:!0,value:T[T.length-1]}}const{finalKeyframe:s,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:u,calculatedDuration:c,totalDuration:d,resolvedDuration:h}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:g,repeatType:v,repeatDelay:x,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),m=this.speed>=0?p<0:p>d;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let y=this.currentTime,k=i;if(g){const T=Math.min(this.currentTime,d)/h;let E=Math.floor(T),A=T%1;!A&&T>=1&&(A=1),A===1&&E--,E=Math.min(E,g+1),!!(E%2)&&(v==="reverse"?(A=1-A,x&&(A-=x/h)):v==="mirror"&&(k=o)),y=sn(0,1,A)*h}const j=m?{done:!1,value:u[0]}:k.next(y);a&&(j.value=a(j.value));let{done:C}=j;!m&&c!==null&&(C=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const _=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&C);return _&&s!==void 0&&(j.value=wa(u,this.options,s)),S&&S(j.value),_&&this.finish(),j}get duration(){const{resolved:t}=this;return t?Zt(t.calculatedDuration):0}get time(){return Zt(this.currentTime)}set time(t){t=Yt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Zt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=R2,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=s):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const F2=new Set(["opacity","clipPath","filter","transform"]);function I2(e,t,n,{delay:r=0,duration:s=300,repeat:i=0,repeatType:o="loop",ease:a="easeInOut",times:u}={}){const c={[t]:n};u&&(c.offset=u);const d=O0(a,s);return Array.isArray(d)&&(c.easing=d),e.animate(c,{delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:i+1,direction:o==="reverse"?"alternate":"normal"})}const O2=Bc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Uo=10,z2=2e4;function $2(e){return sd(e.type)||e.type==="spring"||!I0(e.ease)}function U2(e,t){const n=new yd({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const s=[];let i=0;for(;!r.done&&i<z2;)r=n.sample(i),s.push(r.value),i+=Uo;return{times:void 0,keyframes:s,duration:i-Uo,ease:"linear"}}const wy={anticipate:Y0,backInOut:X0,circInOut:J0};function B2(e){return e in wy}class Eh extends my{constructor(t){super(t);const{name:n,motionValue:r,element:s,keyframes:i}=this.options;this.resolver=new hy(i,(o,a)=>this.onKeyframesResolved(o,a),n,r,s),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:s,ease:i,type:o,motionValue:a,name:u,startTime:c}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof i=="string"&&Oo()&&B2(i)&&(i=wy[i]),$2(this.options)){const{onComplete:h,onUpdate:f,motionValue:g,element:v,...x}=this.options,S=U2(t,x);t=S.keyframes,t.length===1&&(t[1]=t[0]),r=S.duration,s=S.times,i=S.ease,o="keyframes"}const d=I2(a.owner.current,u,t,{...this.options,duration:r,times:s,ease:i});return d.startTime=c??this.calcStartTime(),this.pendingTimeline?(hh(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:h}=this.options;a.set(wa(t,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:s,type:o,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Zt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Zt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Yt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return st;const{animation:r}=n;hh(r,t)}return st}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:s,type:i,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:d,onComplete:h,element:f,...g}=this.options,v=new yd({...g,keyframes:r,duration:s,type:i,ease:o,times:a,isGenerator:!0}),x=Yt(this.time);c.setWithVelocity(v.sample(x-Uo).value,v.sample(x).value,Uo)}const{onStop:u}=this.options;u&&u(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:s,repeatType:i,damping:o,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:u,transformTemplate:c}=n.owner.getProps();return O2()&&r&&F2.has(r)&&!u&&!c&&!s&&i!=="mirror"&&o!==0&&a!=="inertia"}}const H2={type:"spring",stiffness:500,damping:25,restSpeed:10},W2=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),K2={type:"keyframes",duration:.8},G2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},q2=(e,{keyframes:t})=>t.length>2?K2:fr.has(e)?e.startsWith("scale")?W2(t[1]):H2:G2;function Q2({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:o,repeatDelay:a,from:u,elapsed:c,...d}){return!!Object.keys(d).length}const vd=(e,t,n,r={},s,i)=>o=>{const a=rd(r,e)||{},u=a.delay||r.delay||0;let{elapsed:c=0}=r;c=c-Yt(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-c,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:i?void 0:s};Q2(a)||(d={...d,...q2(e,d)}),d.duration&&(d.duration=Yt(d.duration)),d.repeatDelay&&(d.repeatDelay=Yt(d.repeatDelay)),d.from!==void 0&&(d.keyframes[0]=d.from);let h=!1;if((d.type===!1||d.duration===0&&!d.repeatDelay)&&(d.duration=0,d.delay===0&&(h=!0)),h&&!i&&t.get()!==void 0){const f=wa(d.keyframes,a);if(f!==void 0)return ue.update(()=>{d.onUpdate(f),d.onComplete()}),new mS([])}return!i&&Eh.supports(d)?new Eh(d):new yd(d)};function X2({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Sy(e,t,{delay:n=0,transitionOverride:r,type:s}={}){var i;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...u}=t;r&&(o=r);const c=[],d=s&&e.animationState&&e.animationState.getState()[s];for(const h in u){const f=e.getValue(h,(i=e.latestValues[h])!==null&&i!==void 0?i:null),g=u[h];if(g===void 0||d&&X2(d,h))continue;const v={delay:n,...rd(o||{},h)};let x=!1;if(window.MotionHandoffAnimation){const p=W0(e);if(p){const m=window.MotionHandoffAnimation(p,h,ue);m!==null&&(v.startTime=m,x=!0)}}bu(e,h),f.start(vd(h,f,g,e.shouldReduceMotion&&B0.has(h)?{type:!1}:v,e,x));const S=f.animation;S&&c.push(S)}return a&&Promise.all(c).then(()=>{ue.update(()=>{a&&_S(e,a)})}),c}function Eu(e,t,n={}){var r;const s=xa(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(Sy(e,s,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:d=0,staggerChildren:h,staggerDirection:f}=i;return Y2(e,t,d+c,h,f,n)}:()=>Promise.resolve(),{when:u}=i;if(u){const[c,d]=u==="beforeChildren"?[o,a]:[a,o];return c().then(()=>d())}else return Promise.all([o(),a(n.delay)])}function Y2(e,t,n=0,r=0,s=1,i){const o=[],a=(e.variantChildren.size-1)*r,u=s===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(e.variantChildren).sort(Z2).forEach((c,d)=>{c.notify("AnimationStart",t),o.push(Eu(c,t,{...i,delay:n+u(d)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(o)}function Z2(e,t){return e.sortNodePosition(t)}function J2(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>Eu(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=Eu(e,t,n);else{const s=typeof t=="function"?xa(e,t,n.custom):t;r=Promise.all(Sy(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const eb=Wc.length;function by(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?by(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<eb;n++){const r=Wc[n],s=e.props[r];(ri(s)||s===!1)&&(t[r]=s)}return t}const tb=[...Hc].reverse(),nb=Hc.length;function rb(e){return t=>Promise.all(t.map(({animation:n,options:r})=>J2(e,n,r)))}function sb(e){let t=rb(e),n=Ph(),r=!0;const s=u=>(c,d)=>{var h;const f=xa(e,d,u==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:g,transitionEnd:v,...x}=f;c={...c,...x,...v}}return c};function i(u){t=u(e)}function o(u){const{props:c}=e,d=by(e.parent)||{},h=[],f=new Set;let g={},v=1/0;for(let S=0;S<nb;S++){const p=tb[S],m=n[p],y=c[p]!==void 0?c[p]:d[p],k=ri(y),j=p===u?m.isActive:null;j===!1&&(v=S);let C=y===d[p]&&y!==c[p]&&k;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),m.protectedKeys={...g},!m.isActive&&j===null||!y&&!m.prevProp||ya(y)||typeof y=="boolean")continue;const _=ib(m.prevProp,y);let T=_||p===u&&m.isActive&&!C&&k||S>v&&k,E=!1;const A=Array.isArray(y)?y:[y];let R=A.reduce(s(p),{});j===!1&&(R={});const{prevResolvedValues:D={}}=m,F={...D,...R},Y=U=>{T=!0,f.has(U)&&(E=!0,f.delete(U)),m.needsAnimating[U]=!0;const L=e.getValue(U);L&&(L.liveStyle=!1)};for(const U in F){const L=R[U],W=D[U];if(g.hasOwnProperty(U))continue;let X=!1;xu(L)&&xu(W)?X=!L0(L,W):X=L!==W,X?L!=null?Y(U):f.add(U):L!==void 0&&f.has(U)?Y(U):m.protectedKeys[U]=!0}m.prevProp=y,m.prevResolvedValues=R,m.isActive&&(g={...g,...R}),r&&e.blockInitialAnimation&&(T=!1),T&&(!(C&&_)||E)&&h.push(...A.map(U=>({animation:U,options:{type:p}})))}if(f.size){const S={};f.forEach(p=>{const m=e.getBaseTarget(p),y=e.getValue(p);y&&(y.liveStyle=!0),S[p]=m??null}),h.push({animation:S})}let x=!!h.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(h):Promise.resolve()}function a(u,c){var d;if(n[u].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var g;return(g=f.animationState)===null||g===void 0?void 0:g.setActive(u,c)}),n[u].isActive=c;const h=o(u);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:o,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=Ph(),r=!0}}}function ib(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!L0(t,e):!1}function In(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ph(){return{animate:In(!0),whileInView:In(),whileHover:In(),whileTap:In(),whileDrag:In(),whileFocus:In(),exit:In()}}class Rn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ob extends Rn{constructor(t){super(t),t.animationState||(t.animationState=sb(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();ya(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let ab=0;class lb extends Rn{constructor(){super(...arguments),this.id=ab++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const ub={animation:{Feature:ob},exit:{Feature:lb}};function ai(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Si(e){return{point:{x:e.pageX,y:e.pageY}}}const cb=e=>t=>od(t)&&e(t,Si(t));function Ls(e,t,n,r){return ai(e,t,cb(n),r)}const Ah=(e,t)=>Math.abs(e-t);function db(e,t){const n=Ah(e.x,t.x),r=Ah(e.y,t.y);return Math.sqrt(n**2+r**2)}class ky{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=dl(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=db(h.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:v}=h,{timestamp:x}=De;this.history.push({...v,timestamp:x});const{onStart:S,onMove:p}=this.handlers;f||(S&&S(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=cl(f,this.transformPagePoint),ue.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=dl(h.type==="pointercancel"?this.lastMoveEventInfo:cl(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(h,S),v&&v(h,S)},!od(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const o=Si(t),a=cl(o,this.transformPagePoint),{point:u}=a,{timestamp:c}=De;this.history=[{...u,timestamp:c}];const{onSessionStart:d}=n;d&&d(t,dl(a,this.history)),this.removeListeners=wi(Ls(this.contextWindow,"pointermove",this.handlePointerMove),Ls(this.contextWindow,"pointerup",this.handlePointerUp),Ls(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),_n(this.updatePoint)}}function cl(e,t){return t?{point:t(e.point)}:e}function Dh(e,t){return{x:e.x-t.x,y:e.y-t.y}}function dl({point:e},t){return{point:e,delta:Dh(e,Ty(t)),offset:Dh(e,fb(t)),velocity:hb(t,.1)}}function fb(e){return e[0]}function Ty(e){return e[e.length-1]}function hb(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=Ty(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Yt(t)));)n--;if(!r)return{x:0,y:0};const i=Zt(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const o={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const jy=1e-4,mb=1-jy,pb=1+jy,Cy=.01,gb=0-Cy,yb=0+Cy;function ot(e){return e.max-e.min}function vb(e,t,n){return Math.abs(e-t)<=n}function Mh(e,t,n,r=.5){e.origin=r,e.originPoint=me(t.min,t.max,e.origin),e.scale=ot(n)/ot(t),e.translate=me(n.min,n.max,e.origin)-e.originPoint,(e.scale>=mb&&e.scale<=pb||isNaN(e.scale))&&(e.scale=1),(e.translate>=gb&&e.translate<=yb||isNaN(e.translate))&&(e.translate=0)}function Vs(e,t,n,r){Mh(e.x,t.x,n.x,r?r.originX:void 0),Mh(e.y,t.y,n.y,r?r.originY:void 0)}function Rh(e,t,n){e.min=n.min+t.min,e.max=e.min+ot(t)}function xb(e,t,n){Rh(e.x,t.x,n.x),Rh(e.y,t.y,n.y)}function Lh(e,t,n){e.min=t.min-n.min,e.max=e.min+ot(t)}function Fs(e,t,n){Lh(e.x,t.x,n.x),Lh(e.y,t.y,n.y)}function wb(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?me(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?me(n,e,r.max):Math.min(e,n)),e}function Vh(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Sb(e,{top:t,left:n,bottom:r,right:s}){return{x:Vh(e.x,n,s),y:Vh(e.y,t,r)}}function Fh(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function bb(e,t){return{x:Fh(e.x,t.x),y:Fh(e.y,t.y)}}function kb(e,t){let n=.5;const r=ot(e),s=ot(t);return s>r?n=Gr(t.min,t.max-r,e.min):r>s&&(n=Gr(e.min,e.max-s,t.min)),sn(0,1,n)}function Tb(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Pu=.35;function jb(e=Pu){return e===!1?e=0:e===!0&&(e=Pu),{x:Ih(e,"left","right"),y:Ih(e,"top","bottom")}}function Ih(e,t,n){return{min:Oh(e,t),max:Oh(e,n)}}function Oh(e,t){return typeof e=="number"?e:e[t]||0}const zh=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ar=()=>({x:zh(),y:zh()}),$h=()=>({min:0,max:0}),xe=()=>({x:$h(),y:$h()});function ht(e){return[e("x"),e("y")]}function Ny({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Cb({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Nb(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function fl(e){return e===void 0||e===1}function Au({scale:e,scaleX:t,scaleY:n}){return!fl(e)||!fl(t)||!fl(n)}function Un(e){return Au(e)||_y(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function _y(e){return Uh(e.x)||Uh(e.y)}function Uh(e){return e&&e!=="0%"}function Bo(e,t,n){const r=e-n,s=t*r;return n+s}function Bh(e,t,n,r,s){return s!==void 0&&(e=Bo(e,s,r)),Bo(e,n,r)+t}function Du(e,t=0,n=1,r,s){e.min=Bh(e.min,t,n,r,s),e.max=Bh(e.max,t,n,r,s)}function Ey(e,{x:t,y:n}){Du(e.x,t.translate,t.scale,t.originPoint),Du(e.y,n.translate,n.scale,n.originPoint)}const Hh=.999999999999,Wh=1.0000000000001;function _b(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,o;for(let a=0;a<s;a++){i=n[a],o=i.projectionDelta;const{visualElement:u}=i.options;u&&u.props.style&&u.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Mr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ey(e,o)),r&&Un(i.latestValues)&&Mr(e,i.latestValues))}t.x<Wh&&t.x>Hh&&(t.x=1),t.y<Wh&&t.y>Hh&&(t.y=1)}function Dr(e,t){e.min=e.min+t,e.max=e.max+t}function Kh(e,t,n,r,s=.5){const i=me(e.min,e.max,s);Du(e,t,n,i,r)}function Mr(e,t){Kh(e.x,t.x,t.scaleX,t.scale,t.originX),Kh(e.y,t.y,t.scaleY,t.scale,t.originY)}function Py(e,t){return Ny(Nb(e.getBoundingClientRect(),t))}function Eb(e,t,n){const r=Py(e,n),{scroll:s}=t;return s&&(Dr(r.x,s.offset.x),Dr(r.y,s.offset.y)),r}const Ay=({current:e})=>e?e.ownerDocument.defaultView:null,Pb=new WeakMap;class Ab{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=xe(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=d=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Si(d).point)},i=(d,h)=>{const{drag:f,dragPropagation:g,onDragStart:v}=this.getProps();if(f&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=kS(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ht(S=>{let p=this.getAxisMotionValue(S).get()||0;if(It.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const y=m.layout.layoutBox[S];y&&(p=ot(y)*(parseFloat(p)/100))}}this.originPoint[S]=p}),v&&ue.postRender(()=>v(d,h)),bu(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(d,h)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:S}=h;if(g&&this.currentDirection===null){this.currentDirection=Db(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",h.point,S),this.updateAxis("y",h.point,S),this.visualElement.render(),x&&x(d,h)},a=(d,h)=>this.stop(d,h),u=()=>ht(d=>{var h;return this.getAnimationState(d)==="paused"&&((h=this.getAxisMotionValue(d).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new ky(t,{onSessionStart:s,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Ay(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&ue.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!Hi(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=wb(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Er(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=Sb(s.layoutBox,n):this.constraints=!1,this.elastic=jb(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&ht(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Tb(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Er(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=Eb(r,s.root,this.visualElement.getTransformPagePoint());let o=bb(s.layout.layoutBox,i);if(n){const a=n(Cb(o));this.hasMutatedConstraints=!!a,a&&(o=Ny(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),u=this.constraints||{},c=ht(d=>{if(!Hi(d,n,this.currentDirection))return;let h=u&&u[d]||{};o&&(h={min:0,max:0});const f=s?200:1e6,g=s?40:1e7,v={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...i,...h};return this.startAxisValueAnimation(d,v)});return Promise.all(c).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return bu(this.visualElement,t),r.start(vd(t,r,0,n,this.visualElement,!1))}stopAnimation(){ht(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ht(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ht(n=>{const{drag:r}=this.getProps();if(!Hi(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[n];i.set(t[n]-me(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Er(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};ht(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const u=a.get();s[o]=kb({min:u,max:u},this.constraints[o])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ht(o=>{if(!Hi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:u,max:c}=this.constraints[o];a.set(me(u,c,s[o]))})}addListeners(){if(!this.visualElement.current)return;Pb.set(this.visualElement,this);const t=this.visualElement.current,n=Ls(t,"pointerdown",u=>{const{drag:c,dragListener:d=!0}=this.getProps();c&&d&&this.start(u)}),r=()=>{const{dragConstraints:u}=this.getProps();Er(u)&&u.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ue.read(r);const o=ai(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(ht(d=>{const h=this.getAxisMotionValue(d);h&&(this.originPoint[d]+=u[d].translate,h.set(h.get()+u[d].translate))}),this.visualElement.render())});return()=>{o(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:o=Pu,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function Hi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Db(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Mb extends Rn{constructor(t){super(t),this.removeGroupControls=st,this.removeListeners=st,this.controls=new Ab(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||st}unmount(){this.removeGroupControls(),this.removeListeners()}}const Gh=e=>(t,n)=>{e&&ue.postRender(()=>e(t,n))};class Rb extends Rn{constructor(){super(...arguments),this.removePointerDownListener=st}onPointerDown(t){this.session=new ky(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ay(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Gh(t),onStart:Gh(n),onMove:r,onEnd:(i,o)=>{delete this.session,s&&ue.postRender(()=>s(i,o))}}}mount(){this.removePointerDownListener=Ls(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const uo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function qh(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const hs={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(Q.test(e))e=parseFloat(e);else return e;const n=qh(e,t.target.x),r=qh(e,t.target.y);return`${n}% ${r}%`}},Lb={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=En.parse(e);if(s.length>5)return r;const i=En.createTransformer(e),o=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,u=n.y.scale*t.y;s[0+o]/=a,s[1+o]/=u;const c=me(a,u,.5);return typeof s[2+o]=="number"&&(s[2+o]/=c),typeof s[3+o]=="number"&&(s[3+o]/=c),i(s)}};class Vb extends w.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;nS(Fb),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),uo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,s||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||ue.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Gc.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Dy(e){const[t,n]=m0(),r=w.useContext(Oc);return l.jsx(Vb,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(b0),isPresent:t,safeToRemove:n})}const Fb={borderRadius:{...hs,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:hs,borderTopRightRadius:hs,borderBottomLeftRadius:hs,borderBottomRightRadius:hs,boxShadow:Lb};function Ib(e,t,n){const r=ze(e)?e:ii(e);return r.start(vd("",r,t,n)),r.animation}function Ob(e){return e instanceof SVGElement&&e.tagName!=="svg"}const zb=(e,t)=>e.depth-t.depth;class $b{constructor(){this.children=[],this.isDirty=!1}add(t){ad(this.children,t),this.isDirty=!0}remove(t){ld(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(zb),this.isDirty=!1,this.children.forEach(t)}}function Ub(e,t){const n=Ot.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(_n(r),e(i-t))};return ue.read(r,!0),()=>_n(r)}const My=["TopLeft","TopRight","BottomLeft","BottomRight"],Bb=My.length,Qh=e=>typeof e=="string"?parseFloat(e):e,Xh=e=>typeof e=="number"||Q.test(e);function Hb(e,t,n,r,s,i){s?(e.opacity=me(0,n.opacity!==void 0?n.opacity:1,Wb(r)),e.opacityExit=me(t.opacity!==void 0?t.opacity:1,0,Kb(r))):i&&(e.opacity=me(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<Bb;o++){const a=`border${My[o]}Radius`;let u=Yh(t,a),c=Yh(n,a);if(u===void 0&&c===void 0)continue;u||(u=0),c||(c=0),u===0||c===0||Xh(u)===Xh(c)?(e[a]=Math.max(me(Qh(u),Qh(c),r),0),(It.test(c)||It.test(u))&&(e[a]+="%")):e[a]=c}(t.rotate||n.rotate)&&(e.rotate=me(t.rotate||0,n.rotate||0,r))}function Yh(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Wb=Ry(0,.5,Z0),Kb=Ry(.5,.95,st);function Ry(e,t,n){return r=>r<e?0:r>t?1:n(Gr(e,t,r))}function Zh(e,t){e.min=t.min,e.max=t.max}function ft(e,t){Zh(e.x,t.x),Zh(e.y,t.y)}function Jh(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function em(e,t,n,r,s){return e-=t,e=Bo(e,1/n,r),s!==void 0&&(e=Bo(e,1/s,r)),e}function Gb(e,t=0,n=1,r=.5,s,i=e,o=e){if(It.test(t)&&(t=parseFloat(t),t=me(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=me(i.min,i.max,r);e===i&&(a-=t),e.min=em(e.min,t,n,a,s),e.max=em(e.max,t,n,a,s)}function tm(e,t,[n,r,s],i,o){Gb(e,t[n],t[r],t[s],t.scale,i,o)}const qb=["x","scaleX","originX"],Qb=["y","scaleY","originY"];function nm(e,t,n,r){tm(e.x,t,qb,n?n.x:void 0,r?r.x:void 0),tm(e.y,t,Qb,n?n.y:void 0,r?r.y:void 0)}function rm(e){return e.translate===0&&e.scale===1}function Ly(e){return rm(e.x)&&rm(e.y)}function sm(e,t){return e.min===t.min&&e.max===t.max}function Xb(e,t){return sm(e.x,t.x)&&sm(e.y,t.y)}function im(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Vy(e,t){return im(e.x,t.x)&&im(e.y,t.y)}function om(e){return ot(e.x)/ot(e.y)}function am(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Yb{constructor(){this.members=[]}add(t){ad(this.members,t),t.scheduleRender()}remove(t){if(ld(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Zb(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((s||i||o)&&(r=`translate3d(${s}px, ${i}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:c,rotate:d,rotateX:h,rotateY:f,skewX:g,skewY:v}=n;c&&(r=`perspective(${c}px) ${r}`),d&&(r+=`rotate(${d}deg) `),h&&(r+=`rotateX(${h}deg) `),f&&(r+=`rotateY(${f}deg) `),g&&(r+=`skewX(${g}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,u=e.y.scale*t.y;return(a!==1||u!==1)&&(r+=`scale(${a}, ${u})`),r||"none"}const Bn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},ks=typeof window<"u"&&window.MotionDebug!==void 0,hl=["","X","Y","Z"],Jb={visibility:"hidden"},lm=1e3;let ek=0;function ml(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Fy(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=W0(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ue,!(s||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Fy(r)}function Iy({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=ek++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ks&&(Bn.totalNodes=Bn.resolvedTargetDeltas=Bn.recalculatedProjection=0),this.nodes.forEach(rk),this.nodes.forEach(lk),this.nodes.forEach(uk),this.nodes.forEach(sk),ks&&window.MotionDebug.record(Bn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new $b)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new ud),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const u=this.eventHandlers.get(o);u&&u.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Ob(o),this.instance=o;const{layoutId:u,layout:c,visualElement:d}=this.options;if(d&&!d.current&&d.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||u)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Ub(f,250),uo.hasAnimatedSinceResize&&(uo.hasAnimatedSinceResize=!1,this.nodes.forEach(cm))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&d&&(u||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||mk,{onLayoutAnimationStart:S,onLayoutAnimationComplete:p}=d.getProps(),m=!this.targetLayout||!Vy(this.targetLayout,v)||g,y=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||f&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,y);const k={...rd(x,"layout"),onPlay:S,onComplete:p};(d.shouldReduceMotion||this.options.layoutRoot)&&(k.delay=0,k.type=!1),this.startAnimation(k)}else f||cm(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,_n(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ck),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Fy(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const h=this.path[d];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:u}=this.options;if(a===void 0&&!u)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(um);return}this.isUpdating||this.nodes.forEach(ok),this.isUpdating=!1,this.nodes.forEach(ak),this.nodes.forEach(tk),this.nodes.forEach(nk),this.clearAllSnapshots();const a=Ot.now();De.delta=sn(0,1e3/60,a-De.timestamp),De.timestamp=a,De.isProcessing=!0,sl.update.process(De),sl.preRender.process(De),sl.render.process(De),De.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Gc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ik),this.sharedNodes.forEach(dk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ue.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ue.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=xe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const u=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:u,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:u}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Ly(this.projectionDelta),u=this.getTransformTemplate(),c=u?u(this.latestValues,""):void 0,d=c!==this.prevTransformTemplateValue;o&&(a||Un(this.latestValues)||d)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let u=this.removeElementScroll(a);return o&&(u=this.removeTransform(u)),pk(u),{animationId:this.root.animationId,measuredBox:a,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return xe();const u=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(gk))){const{scroll:d}=this.root;d&&(Dr(u.x,d.offset.x),Dr(u.y,d.offset.y))}return u}removeElementScroll(o){var a;const u=xe();if(ft(u,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return u;for(let c=0;c<this.path.length;c++){const d=this.path[c],{scroll:h,options:f}=d;d!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&ft(u,o),Dr(u.x,h.offset.x),Dr(u.y,h.offset.y))}return u}applyTransform(o,a=!1){const u=xe();ft(u,o);for(let c=0;c<this.path.length;c++){const d=this.path[c];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Mr(u,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Un(d.latestValues)&&Mr(u,d.latestValues)}return Un(this.latestValues)&&Mr(u,this.latestValues),u}removeTransform(o){const a=xe();ft(a,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!Un(c.latestValues))continue;Au(c.latestValues)&&c.updateSnapshot();const d=xe(),h=c.measurePageBox();ft(d,h),nm(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return Un(this.latestValues)&&nm(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==De.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const u=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=u.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=u.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=u.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==u;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=De.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Fs(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),ft(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=xe(),this.targetWithTransforms=xe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xb(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ft(this.target,this.layout.layoutBox),Ey(this.target,this.targetDelta)):ft(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Fs(this.relativeTargetOrigin,this.target,g.target),ft(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ks&&Bn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Au(this.parent.latestValues)||_y(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),u=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(c=!1),u&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===De.timestamp&&(c=!1),c)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;ft(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;_b(this.layoutCorrected,this.treeScale,this.path,u),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=xe());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Jh(this.prevProjectionDelta.x,this.projectionDelta.x),Jh(this.prevProjectionDelta.y,this.projectionDelta.y)),Vs(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==g||!am(this.projectionDelta.x,this.prevProjectionDelta.x)||!am(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),ks&&Bn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const u=this.getStack();u&&u.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Ar(),this.projectionDelta=Ar(),this.projectionDeltaWithTransform=Ar()}setAnimationOrigin(o,a=!1){const u=this.snapshot,c=u?u.latestValues:{},d={...this.latestValues},h=Ar();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=xe(),g=u?u.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,S=this.getStack(),p=!S||S.members.length<=1,m=!!(x&&!p&&this.options.crossfade===!0&&!this.path.some(hk));this.animationProgress=0;let y;this.mixTargetDelta=k=>{const j=k/1e3;dm(h.x,o.x,j),dm(h.y,o.y,j),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fs(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),fk(this.relativeTarget,this.relativeTargetOrigin,f,j),y&&Xb(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=xe()),ft(y,this.relativeTarget)),x&&(this.animationValues=d,Hb(d,c,this.latestValues,j,m,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(_n(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ue.update(()=>{uo.hasAnimatedSinceResize=!0,this.currentAnimation=Ib(0,lm,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(lm),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:u,layout:c,latestValues:d}=o;if(!(!a||!u||!c)){if(this!==o&&this.layout&&c&&Oy(this.options.animationType,this.layout.layoutBox,c.layoutBox)){u=this.target||xe();const h=ot(this.layout.layoutBox.x);u.x.min=o.target.x.min,u.x.max=u.x.min+h;const f=ot(this.layout.layoutBox.y);u.y.min=o.target.y.min,u.y.max=u.y.min+f}ft(a,u),Mr(a,d),Vs(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Yb),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:u}={}){const c=this.getStack();c&&c.promote(this,u),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:u}=o;if((u.z||u.rotate||u.rotateX||u.rotateY||u.rotateZ||u.skewX||u.skewY)&&(a=!0),!a)return;const c={};u.z&&ml("z",o,c,this.animationValues);for(let d=0;d<hl.length;d++)ml(`rotate${hl[d]}`,o,c,this.animationValues),ml(`skew${hl[d]}`,o,c,this.animationValues);o.render();for(const d in c)o.setStaticValue(d,c[d]),this.animationValues&&(this.animationValues[d]=c[d]);o.scheduleRender()}getProjectionStyles(o){var a,u;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Jb;const c={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=ao(o==null?void 0:o.pointerEvents)||"",c.transform=d?d(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=ao(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Un(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Zb(this.projectionDeltaWithTransform,this.treeScale,f),d&&(c.transform=d(f,c.transform));const{x:g,y:v}=this.projectionDelta;c.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,h.animationValues?c.opacity=h===this?(u=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in Io){if(f[x]===void 0)continue;const{correct:S,applyTo:p}=Io[x],m=c.transform==="none"?f[x]:S(f[x],h);if(p){const y=p.length;for(let k=0;k<y;k++)c[p[k]]=m}else c[x]=m}return this.options.layoutId&&(c.pointerEvents=h===this?ao(o==null?void 0:o.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(um),this.root.sharedNodes.clear()}}}function tk(e){e.updateLayout()}function nk(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;i==="size"?ht(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],g=ot(f);f.min=r[h].min,f.max=f.min+g}):Oy(i,n.layoutBox,r)&&ht(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],g=ot(r[h]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+g)});const a=Ar();Vs(a,r,n.layoutBox);const u=Ar();o?Vs(u,e.applyTransform(s,!0),n.measuredBox):Vs(u,r,n.layoutBox);const c=!Ly(a);let d=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:g}=h;if(f&&g){const v=xe();Fs(v,n.layoutBox,f.layoutBox);const x=xe();Fs(x,r,g.layoutBox),Vy(v,x)||(d=!0),h.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:u,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function rk(e){ks&&Bn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function sk(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ik(e){e.clearSnapshot()}function um(e){e.clearMeasurements()}function ok(e){e.isLayoutDirty=!1}function ak(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function cm(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function lk(e){e.resolveTargetDelta()}function uk(e){e.calcProjection()}function ck(e){e.resetSkewAndRotation()}function dk(e){e.removeLeadSnapshot()}function dm(e,t,n){e.translate=me(t.translate,0,n),e.scale=me(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function fm(e,t,n,r){e.min=me(t.min,n.min,r),e.max=me(t.max,n.max,r)}function fk(e,t,n,r){fm(e.x,t.x,n.x,r),fm(e.y,t.y,n.y,r)}function hk(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const mk={duration:.45,ease:[.4,0,.1,1]},hm=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),mm=hm("applewebkit/")&&!hm("chrome/")?Math.round:st;function pm(e){e.min=mm(e.min),e.max=mm(e.max)}function pk(e){pm(e.x),pm(e.y)}function Oy(e,t,n){return e==="position"||e==="preserve-aspect"&&!vb(om(t),om(n),.2)}function gk(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const yk=Iy({attachResizeListener:(e,t)=>ai(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),pl={current:void 0},zy=Iy({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!pl.current){const e=new yk({});e.mount(window),e.setOptions({layoutScroll:!0}),pl.current=e}return pl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),vk={pan:{Feature:Rb},drag:{Feature:Mb,ProjectionNode:zy,MeasureLayout:Dy}};function gm(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,i=r[s];i&&ue.postRender(()=>i(t,Si(t)))}class xk extends Rn{mount(){const{current:t}=this.node;t&&(this.unmount=vS(t,n=>(gm(this.node,n,"Start"),r=>gm(this.node,r,"End"))))}unmount(){}}class wk extends Rn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=wi(ai(this.node.current,"focus",()=>this.onFocus()),ai(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ym(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),i=r[s];i&&ue.postRender(()=>i(t,Si(t)))}class Sk extends Rn{mount(){const{current:t}=this.node;t&&(this.unmount=bS(t,n=>(ym(this.node,n,"Start"),(r,{success:s})=>ym(this.node,r,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Mu=new WeakMap,gl=new WeakMap,bk=e=>{const t=Mu.get(e.target);t&&t(e)},kk=e=>{e.forEach(bk)};function Tk({root:e,...t}){const n=e||document;gl.has(n)||gl.set(n,{});const r=gl.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(kk,{root:e,...t})),r[s]}function jk(e,t,n){const r=Tk(t);return Mu.set(e,n),r.observe(e),()=>{Mu.delete(e),r.unobserve(e)}}const Ck={some:0,all:1};class Nk extends Rn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:Ck[s]},a=u=>{const{isIntersecting:c}=u;if(this.isInView===c||(this.isInView=c,i&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:d,onViewportLeave:h}=this.node.getProps(),f=c?d:h;f&&f(u)};return jk(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(_k(t,n))&&this.startObserver()}unmount(){}}function _k({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Ek={inView:{Feature:Nk},tap:{Feature:Sk},focus:{Feature:wk},hover:{Feature:xk}},Pk={layout:{ProjectionNode:zy,MeasureLayout:Dy}},Ru={current:null},$y={current:!1};function Ak(){if($y.current=!0,!!Uc)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ru.current=e.matches;e.addListener(t),t()}else Ru.current=!1}const Dk=[...fy,Fe,En],Mk=e=>Dk.find(dy(e)),vm=new WeakMap;function Rk(e,t,n){for(const r in t){const s=t[r],i=n[r];if(ze(s))e.addValue(r,s);else if(ze(i))e.addValue(r,ii(s,{owner:e}));else if(i!==s)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=e.getStaticValue(r);e.addValue(r,ii(o!==void 0?o:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const xm=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Lk{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=pd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=Ot.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,ue.render(this.render,!1,!0))};const{latestValues:u,renderState:c,onUpdate:d}=o;this.onUpdate=d,this.latestValues=u,this.baseTarget={...u},this.initialValues=n.initial?{...u}:{},this.renderState=c,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=va(n),this.isVariantNode=w0(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:h,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in f){const v=f[g];u[g]!==void 0&&ze(v)&&v.set(u[g],!1)}}mount(t){this.current=t,vm.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),$y.current||Ak(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ru.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){vm.delete(this.current),this.projection&&this.projection.unmount(),_n(this.notifyUpdate),_n(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=fr.has(t),s=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&ue.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),i(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in qr){const n=qr[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):xe()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<xm.length;r++){const s=xm[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i="on"+s,o=t[i];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=Rk(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ii(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let s=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return s!=null&&(typeof s=="string"&&(uy(s)||ey(s))?s=parseFloat(s):!Mk(s)&&En.test(n)&&(s=oy(t,n)),this.setBaseTarget(t,ze(s)?s.get():s)),ze(s)?s.get():s}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let s;if(typeof r=="string"||typeof r=="object"){const o=Qc(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(s=o[t])}if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!ze(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ud),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Uy extends Lk{constructor(){super(...arguments),this.KeyframeResolver=hy}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ze(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Vk(e){return window.getComputedStyle(e)}class Fk extends Uy{constructor(){super(...arguments),this.type="html",this.renderInstance=E0}readValueFromInstance(t,n){if(fr.has(n)){const r=md(n);return r&&r.default||0}else{const r=Vk(t),s=(C0(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Py(t,n)}build(t,n,r){Zc(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return nd(t,n,r)}}class Ik extends Uy{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=xe}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(fr.has(n)){const r=md(n);return r&&r.default||0}return n=P0.has(n)?n:Kc(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return M0(t,n,r)}build(t,n,r){Jc(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,s){A0(t,n,r,s)}mount(t){this.isSVGTag=td(t.tagName),super.mount(t)}}const Ok=(e,t)=>qc(e)?new Ik(t):new Fk(t,{allowProjection:e!==w.Fragment}),zk=dS({...ub,...Ek,...vk,...Pk},Ok),P=Cw(zk),Kn=class Kn{constructor(){Ut(this,"session",null);Ut(this,"user",null);Ut(this,"profile",null)}static getInstance(){return Kn.instance||(Kn.instance=new Kn),Kn.instance}async login(t){const n=t.username==="GOD"&&t.password==="123456";if(t.username==="GOD"&&t.password!=="123456")throw new Error("Invalid credentials");const r=n?"test-user-god":"mock-user-1",s={sessionId:"mock-session-"+Date.now(),userId:r,username:t.username,expiresAt:new Date(Date.now()+24*60*60*1e3)},i={id:r,username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},o={user_id:r,full_name:n?"God User (Test Account)":t.username,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.session=s,this.user=i,this.profile=o,localStorage.setItem("authSession",JSON.stringify(s)),s}async register(t){const n={id:"mock-user-"+Date.now(),username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},r={user_id:n.id,full_name:t.username,email:t.email,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.user=n,this.profile=r,{user:n,profile:r}}async logout(){this.session=null,this.user=null,this.profile=null,localStorage.removeItem("authSession")}async validateSession(){try{const t=localStorage.getItem("authSession");if(t){const n=JSON.parse(t);return new Date(n.expiresAt)>new Date?(this.session=n,!0):(localStorage.removeItem("authSession"),!1)}return!1}catch(t){return console.error("Session validation failed:",t),!1}}getSession(){return this.session}getUser(){return this.user}getProfile(){return this.profile}requiresMultiFactorAuth(){return!1}async verifyMFA(t){return this.session}async refreshSession(){return this.session?(this.session.expiresAt=new Date(Date.now()+24*60*60*1e3),localStorage.setItem("authSession",JSON.stringify(this.session)),this.session):null}async changePassword(t,n){console.log("Password changed (mock)")}async initialize(){await this.validateSession()}};Ut(Kn,"instance");let Lu=Kn;const fe=Lu.getInstance();/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $k=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),By=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Uk={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bk=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:o,...a},u)=>w.createElement("svg",{ref:u,...Uk,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:By("lucide",s),...a},[...o.map(([c,d])=>w.createElement(c,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=(e,t)=>{const n=w.forwardRef(({className:r,...s},i)=>w.createElement(Bk,{ref:i,iconNode:t,className:By(`lucide-${$k(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hy=K("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hk=K("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wk=K("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wy=K("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ky=K("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gy=K("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=K("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=K("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kk=K("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=K("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=K("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xd=K("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=K("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ms=K("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wm=K("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qy=K("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gk=K("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qk=K("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=K("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=K("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qk=K("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xy=K("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=K("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xk=K("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sm=K("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wd=K("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sd=K("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bm=K("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=K("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yk=K("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=K("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bd=K("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zk=K("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jk=K("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=K("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eT=K("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tT=K("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=K("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=K("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nT=K("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kd=K("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vu=K("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rT=K("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sT=K("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ev=K("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iT=K("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oT=K("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const km=K("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Td=K("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aT=K("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=K("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lT=K("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=K("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=K("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wt=K("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cd=K("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),uT=({onClose:e,onVerify:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(!1),[o,a]=w.useState(null),[u,c]=w.useState("totp"),d=async f=>{f.preventDefault(),i(!0),a(null);try{await t(n),e()}catch(g){a(g instanceof Error?g.message:"Verification failed")}finally{i(!1)}},h=async()=>{i(!0),a(null);try{await new Promise(f=>setTimeout(f,1e3)),a("Code sent successfully")}catch{a("Failed to send code")}finally{i(!1)}};return l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:f=>f.target===f.currentTarget&&e(),children:l.jsxs(P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"Two-Factor Authentication"}),l.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:l.jsx(wt,{className:"w-6 h-6"})})]}),o&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:o}),l.jsxs("div",{className:"mb-6",children:[l.jsx("p",{className:"fa-body text-gray-600 mb-4",children:"Enter the verification code from your authenticator app"}),l.jsxs("div",{className:"flex space-x-2 mb-6",children:[l.jsxs("button",{onClick:()=>c("totp"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${u==="totp"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[l.jsx(iT,{className:"w-4 h-4 mr-2"}),"Authenticator App"]}),l.jsxs("button",{onClick:()=>c("email"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${u==="email"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[l.jsx(bd,{className:"w-4 h-4 mr-2"}),"Email"]})]})]}),l.jsxs("form",{onSubmit:d,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Verification Code"}),l.jsx("input",{id:"code",type:"text",value:n,onChange:f=>r(f.target.value),className:"fa-input w-full text-center text-lg tracking-widest",placeholder:"000000",maxLength:6,required:!0})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:h,disabled:s,className:"flex-1 fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Resend Code"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:s||n.length!==6,className:"flex-1 fa-button-primary py-3 px-4 rounded-lg font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed",children:s?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Verifying..."]}):"Verify"})]})]}),l.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200",children:l.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[l.jsx(sT,{className:"w-4 h-4 mr-2"}),l.jsx("span",{children:"Two-factor authentication adds an extra layer of security to your account"})]})})]})})},tv=w.createContext(void 0),cT=({children:e})=>{const[t,n]=w.useState(null),[r,s]=w.useState(null),[i,o]=w.useState(null),[a,u]=w.useState(!1),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!0),[x,S]=w.useState(null);w.useEffect(()=>{(async()=>{try{v(!0),await fe.initialize();const E=fe.getSession(),A=fe.getUser(),R=fe.getProfile();n(E),s(A),o(R),u(!!E&&!!A),d(fe.requiresMultiFactorAuth())}catch(E){console.error("Auth initialization error:",E),S("Failed to initialize authentication")}finally{v(!1)}})()},[]);const p=async(T,E)=>{try{v(!0),S(null);const A={username:T,password:E},R=await fe.login(A),D=fe.getUser(),F=fe.getProfile();n(R),s(D),o(F),u(!0),d(!1)}catch(A){if(A instanceof Error&&A.message==="MFA_REQUIRED")d(!0),f(!0),S(null);else{const R=A instanceof Error?A.message:"Login failed";throw S(R),A}}finally{v(!1)}},m=async T=>{try{v(!0),S(null);const E=await fe.verifyMFA(T),A=fe.getUser(),R=fe.getProfile();n(E),s(A),o(R),u(!0),d(!1),f(!1)}catch(E){const A=E instanceof Error?E.message:"MFA verification failed";throw S(A),E}finally{v(!1)}},y=async(T,E,A,R)=>{try{v(!0),S(null);const D={username:T,password:E,email:A,fullName:R};await fe.register(D),await p(T,E)}catch(D){const F=D instanceof Error?D.message:"Registration failed";throw S(F),D}finally{v(!1)}},k=async()=>{try{v(!0),S(null),await fe.logout(),n(null),s(null),o(null),u(!1),d(!1),f(!1)}catch(T){const E=T instanceof Error?T.message:"Logout failed";S(E)}finally{v(!1)}},_={session:t,user:r,profile:i,isAuthenticated:a,requiresMFA:c,login:p,register:y,verifyMFA:m,logout:k,refreshSession:async()=>{try{v(!0),S(null);const T=await fe.refreshSession();T?(n(T),u(!0)):await k()}catch(T){const E=T instanceof Error?T.message:"Session refresh failed";S(E)}finally{v(!1)}},changePassword:async(T,E)=>{try{v(!0),S(null),await fe.changePassword(T,E)}catch(A){const R=A instanceof Error?A.message:"Password change failed";throw S(R),A}finally{v(!1)}},loading:g,error:x};return l.jsxs(tv.Provider,{value:_,children:[e,h&&l.jsx(uT,{onClose:()=>f(!1),onVerify:m})]})},ss=()=>{const e=w.useContext(tv);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},dT=()=>{const{session:e,refreshSession:t,logout:n}=ss(),r=w.useCallback(async()=>{if(e){const s=new Date().getTime();if(new Date(e.expiresAt).getTime()-s<5*60*1e3)try{await t()}catch(o){console.error("Failed to refresh session:",o),await n()}}},[e,t,n]);return w.useEffect(()=>{r();const s=setInterval(r,60*1e3);return()=>clearInterval(s)},[r]),{session:e,checkSessionExpiry:r}},fT=w.createContext(void 0),hT=({children:e})=>{const[t,n]=w.useState(!1),[r,s]=w.useState(null);dT(),w.useEffect(()=>{(async()=>{try{if(window.electronAPI){const a=await window.electronAPI.system.getInfo();s(a)}n(!0)}catch(a){console.error("Failed to initialize application:",a),n(!0)}})()},[]);const i={isInitialized:t,electronAPI:window.electronAPI||null,systemInfo:r};return l.jsx(fT.Provider,{value:i,children:e})},nv=w.createContext(void 0),mT=()=>{const e=w.useContext(nv);if(!e)throw new Error("useTheme must be used within ThemeProvider");return e},pT=({children:e})=>{const[t,n]=w.useState("light");w.useEffect(()=>{const o=localStorage.getItem("fa-theme");if(o)n(o);else{const a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(a)}},[]),w.useEffect(()=>{document.documentElement.setAttribute("data-theme",t),localStorage.setItem("fa-theme",t),t==="dark"?document.body.style.background=`linear-gradient(135deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(59, 130, 246, 0.1) 100%)`:document.body.style.background=`linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1) 0%, 
        rgba(126, 211, 33, 0.05) 50%, 
        rgba(135, 206, 235, 0.1) 100%)`},[t]);const i={theme:t,toggleTheme:()=>{n(o=>o==="light"?"dark":"light")},setTheme:o=>{n(o)}};return l.jsx(nv.Provider,{value:i,children:e})},gT=({onClose:e})=>{const{user:t,profile:n,changePassword:r}=ss(),[s,i]=w.useState(""),[o,a]=w.useState(""),[u,c]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(null),[v,x]=w.useState(!1),S=async p=>{if(p.preventDefault(),o!==u){g({type:"error",text:"New passwords do not match"});return}if(o.length<12){g({type:"error",text:"Password must be at least 12 characters long"});return}x(!0),g(null);try{await r(s,o),g({type:"success",text:"Password changed successfully"}),i(""),a(""),c(""),h(!1)}catch(m){g({type:"error",text:m instanceof Error?m.message:"Failed to change password"})}finally{x(!1)}};return l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:p=>p.target===p.currentTarget&&e(),children:l.jsxs(P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"User Profile"}),l.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:l.jsx(wt,{className:"w-6 h-6"})})]}),f&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-4 p-3 rounded-lg text-sm ${f.type==="success"?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:f.text}),l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex flex-col items-center",children:[l.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mb-4",children:l.jsx(Pn,{className:"w-10 h-10 text-white"})}),l.jsx("h3",{className:"fa-heading-3 text-gray-800",children:t==null?void 0:t.username}),(n==null?void 0:n.full_name)&&l.jsx("p",{className:"fa-body text-gray-600",children:n.full_name})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(Pn,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Username"}),l.jsx("p",{className:"fa-body text-gray-800",children:t==null?void 0:t.username})]})]}),(n==null?void 0:n.email)&&l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(bd,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Email"}),l.jsx("p",{className:"fa-body text-gray-800",children:n.email})]})]}),l.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[l.jsx(Ln,{className:"w-5 h-5 text-gray-500 mr-3"}),l.jsxs("div",{children:[l.jsx("p",{className:"fa-caption text-gray-500",children:"Member since"}),l.jsx("p",{className:"fa-body text-gray-800",children:t!=null&&t.created_at?new Date(t.created_at).toLocaleDateString():"Unknown"})]})]})]}),l.jsxs("div",{className:"pt-4",children:[l.jsxs("button",{onClick:()=>h(!d),className:"w-full fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 flex items-center justify-center",children:[l.jsx(tr,{className:"w-5 h-5 mr-2"}),d?"Cancel":"Change Password"]}),d&&l.jsxs(P.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:S,className:"mt-4 space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),l.jsx("input",{id:"currentPassword",type:"password",value:s,onChange:p=>i(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),l.jsx("input",{id:"newPassword",type:"password",value:o,onChange:p=>a(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),l.jsx("input",{id:"confirmPassword",type:"password",value:u,onChange:p=>c(p.target.value),className:"fa-input w-full",required:!0})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:v,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:v?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Changing..."]}):l.jsxs(l.Fragment,{children:[l.jsx(kd,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})]})})},yT=({systemInfo:e,onToggleTheme:t,theme:n})=>{const{user:r,logout:s}=ss(),[i,o]=w.useState(!1);return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift",onClick:t,children:n==="light"?l.jsx(aT,{className:"w-5 h-5 text-fa-blue-600"}):l.jsx(Jk,{className:"w-5 h-5 text-fa-aqua-400"})}),l.jsx("h1",{className:"fa-heading-3 font-bold text-fa-gray-800",children:"Modern Todo"})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[r&&l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700",onClick:()=>o(!0),children:[l.jsx(Pn,{className:"w-5 h-5"}),l.jsx("span",{className:"ml-2 text-sm font-medium hidden md:inline",children:r.username})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift text-fa-gray-700",onClick:s,children:l.jsx("span",{className:"text-sm font-medium",children:"Logout"})})]}),l.jsxs("div",{className:"text-xs text-fa-gray-500",children:[e==null?void 0:e.platform," • ",e==null?void 0:e.version]})]})]}),i&&l.jsx(gT,{onClose:()=>o(!1)})]})},vT=()=>{const e=[{icon:Sd,label:"Dashboard",active:!0},{icon:Yy,label:"All Tasks"},{icon:Ln,label:"Today"},{icon:Jt,label:"Projects"},{icon:hr,label:"Tags"},{icon:wd,label:"Favorites"},{icon:Cd,label:"Quick Add"}],t=[{name:"Personal",count:12},{name:"Work",count:8},{name:"Shopping",count:3},{name:"Health",count:5}];return l.jsxs("div",{className:"h-full fa-glass-panel rounded-r-2xl flex flex-col",children:[l.jsx("div",{className:"p-4 border-b border-fa-white-frosted",children:l.jsx("h2",{className:"fa-heading-3 font-bold text-fa-gray-800 mb-2",children:"Navigation"})}),l.jsxs("div",{className:"flex-1 overflow-y-auto p-2",children:[l.jsx("nav",{className:"space-y-1",children:e.map((n,r)=>{const s=n.icon;return l.jsxs(P.button,{whileHover:{x:4},whileTap:{scale:.98},className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all ${n.active?"bg-fa-white-frosted text-fa-blue-600 shadow-md":"text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"}`,children:[l.jsx(s,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:n.label})]},r)})}),l.jsxs("div",{className:"mt-8",children:[l.jsxs("div",{className:"flex items-center justify-between px-4 mb-3",children:[l.jsx("h3",{className:"fa-body font-semibold text-fa-gray-700",children:"Categories"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded-lg text-fa-blue-500 hover:bg-fa-white-glass",children:l.jsx(Sa,{className:"w-4 h-4"})})]}),l.jsx("div",{className:"space-y-1",children:t.map((n,r)=>l.jsxs(P.button,{whileHover:{x:4},whileTap:{scale:.98},className:"w-full flex items-center justify-between px-4 py-2 rounded-lg text-left text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[l.jsx("span",{children:n.name}),l.jsx("span",{className:"text-xs bg-fa-white-glass px-2 py-1 rounded-full",children:n.count})]},r))})]})]}),l.jsx("div",{className:"p-4 border-t border-fa-white-frosted",children:l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[l.jsx(rT,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"Settings"})]})})]})},xT={},Tm=e=>{let t;const n=new Set,r=(d,h)=>{const f=typeof d=="function"?d(t):d;if(!Object.is(f,t)){const g=t;t=h??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(v=>v(t,g))}},s=()=>t,u={setState:r,getState:s,getInitialState:()=>c,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(xT?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,s,u);return u},wT=e=>e?Tm(e):Tm;var rv={exports:{}},sv={},iv={exports:{}},ov={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xr=w;function ST(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var bT=typeof Object.is=="function"?Object.is:ST,kT=Xr.useState,TT=Xr.useEffect,jT=Xr.useLayoutEffect,CT=Xr.useDebugValue;function NT(e,t){var n=t(),r=kT({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return jT(function(){s.value=n,s.getSnapshot=t,yl(s)&&i({inst:s})},[e,n,t]),TT(function(){return yl(s)&&i({inst:s}),e(function(){yl(s)&&i({inst:s})})},[e]),CT(n),n}function yl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!bT(e,n)}catch{return!0}}function _T(e,t){return t()}var ET=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?_T:NT;ov.useSyncExternalStore=Xr.useSyncExternalStore!==void 0?Xr.useSyncExternalStore:ET;iv.exports=ov;var PT=iv.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ba=w,AT=PT;function DT(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var MT=typeof Object.is=="function"?Object.is:DT,RT=AT.useSyncExternalStore,LT=ba.useRef,VT=ba.useEffect,FT=ba.useMemo,IT=ba.useDebugValue;sv.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var i=LT(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=FT(function(){function u(g){if(!c){if(c=!0,d=g,g=r(g),s!==void 0&&o.hasValue){var v=o.value;if(s(v,g))return h=v}return h=g}if(v=h,MT(d,g))return v;var x=r(g);return s!==void 0&&s(v,x)?(d=g,v):(d=g,h=x)}var c=!1,d,h,f=n===void 0?null:n;return[function(){return u(t())},f===null?void 0:function(){return u(f())}]},[t,n,r,s]);var a=RT(e,i[0],i[1]);return VT(function(){o.hasValue=!0,o.value=a},[a]),IT(a),a};rv.exports=sv;var OT=rv.exports;const zT=tp(OT),av={},{useDebugValue:$T}=Z,{useSyncExternalStoreWithSelector:UT}=zT;let jm=!1;const BT=e=>e;function HT(e,t=BT,n){(av?"production":void 0)!=="production"&&n&&!jm&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),jm=!0);const r=UT(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return $T(r),r}const WT=e=>{(av?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?wT(e):e,n=(r,s)=>HT(t,r,s);return Object.assign(n,t),n},Nd=e=>WT,KT={},GT=e=>(t,n,r)=>{const s=r.subscribe;return r.subscribe=(o,a,u)=>{let c=o;if(a){const d=(u==null?void 0:u.equalityFn)||Object.is;let h=o(r.getState());c=f=>{const g=o(f);if(!d(h,g)){const v=h;a(h=g,v)}},u!=null&&u.fireImmediately&&a(h,h)}return s(c)},e(t,n,r)},_d=GT;function qT(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var i;const o=u=>u===null?null:JSON.parse(u,void 0),a=(i=n.getItem(s))!=null?i:null;return a instanceof Promise?a.then(o):o(a)},setItem:(s,i)=>n.setItem(s,JSON.stringify(i,void 0)),removeItem:s=>n.removeItem(s)}}const ci=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return ci(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return ci(r)(n)}}}},QT=(e,t)=>(n,r,s)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,p)=>({...p,...S}),...t},o=!1;const a=new Set,u=new Set;let c;try{c=i.getStorage()}catch{}if(!c)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...S)},r,s);const d=ci(i.serialize),h=()=>{const S=i.partialize({...r()});let p;const m=d({state:S,version:i.version}).then(y=>c.setItem(i.name,y)).catch(y=>{p=y});if(p)throw p;return m},f=s.setState;s.setState=(S,p)=>{f(S,p),h()};const g=e((...S)=>{n(...S),h()},r,s);let v;const x=()=>{var S;if(!c)return;o=!1,a.forEach(m=>m(r()));const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,r()))||void 0;return ci(c.getItem.bind(c))(i.name).then(m=>{if(m)return i.deserialize(m)}).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return i.migrate(m.state,m.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return m.state}).then(m=>{var y;return v=i.merge(m,(y=r())!=null?y:g),n(v,!0),h()}).then(()=>{p==null||p(v,void 0),o=!0,u.forEach(m=>m(v))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:S=>{i={...i,...S},S.getStorage&&(c=S.getStorage())},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>x(),hasHydrated:()=>o,onHydrate:S=>(a.add(S),()=>{a.delete(S)}),onFinishHydration:S=>(u.add(S),()=>{u.delete(S)})},x(),v||g},XT=(e,t)=>(n,r,s)=>{let i={storage:qT(()=>localStorage),partialize:x=>x,version:0,merge:(x,S)=>({...S,...x}),...t},o=!1;const a=new Set,u=new Set;let c=i.storage;if(!c)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...x)},r,s);const d=()=>{const x=i.partialize({...r()});return c.setItem(i.name,{state:x,version:i.version})},h=s.setState;s.setState=(x,S)=>{h(x,S),d()};const f=e((...x)=>{n(...x),d()},r,s);s.getInitialState=()=>f;let g;const v=()=>{var x,S;if(!c)return;o=!1,a.forEach(m=>{var y;return m((y=r())!=null?y:f)});const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,(x=r())!=null?x:f))||void 0;return ci(c.getItem.bind(c))(i.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return[!0,i.migrate(m.state,m.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var y;const[k,j]=m;if(g=i.merge(j,(y=r())!=null?y:f),n(g,!0),k)return d()}).then(()=>{p==null||p(g,void 0),g=r(),o=!0,u.forEach(m=>m(g))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:x=>{i={...i,...x},x.storage&&(c=x.storage)},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>o,onHydrate:x=>(a.add(x),()=>{a.delete(x)}),onFinishHydration:x=>(u.add(x),()=>{u.delete(x)})},i.skipHydration||v(),g||f},YT=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((KT?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),QT(e,t)):XT(e,t),Ed=YT;var lv=Symbol.for("immer-nothing"),Cm=Symbol.for("immer-draftable"),at=Symbol.for("immer-state");function Ct(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Yr=Object.getPrototypeOf;function Zr(e){return!!e&&!!e[at]}function lr(e){var t;return e?uv(e)||Array.isArray(e)||!!e[Cm]||!!((t=e.constructor)!=null&&t[Cm])||Ta(e)||ja(e):!1}var ZT=Object.prototype.constructor.toString();function uv(e){if(!e||typeof e!="object")return!1;const t=Yr(e);if(t===null)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===ZT}function Wo(e,t){ka(e)===0?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function ka(e){const t=e[at];return t?t.type_:Array.isArray(e)?1:Ta(e)?2:ja(e)?3:0}function Fu(e,t){return ka(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function cv(e,t,n){const r=ka(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function JT(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function Ta(e){return e instanceof Map}function ja(e){return e instanceof Set}function Wn(e){return e.copy_||e.base_}function Iu(e,t){if(Ta(e))return new Map(e);if(ja(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=uv(e);if(t===!0||t==="class_only"&&!n){const r=Object.getOwnPropertyDescriptors(e);delete r[at];let s=Reflect.ownKeys(r);for(let i=0;i<s.length;i++){const o=s[i],a=r[o];a.writable===!1&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(r[o]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[o]})}return Object.create(Yr(e),r)}else{const r=Yr(e);if(r!==null&&n)return{...e};const s=Object.create(r);return Object.assign(s,e)}}function Pd(e,t=!1){return Ca(e)||Zr(e)||!lr(e)||(ka(e)>1&&(e.set=e.add=e.clear=e.delete=ej),Object.freeze(e),t&&Object.entries(e).forEach(([n,r])=>Pd(r,!0))),e}function ej(){Ct(2)}function Ca(e){return Object.isFrozen(e)}var tj={};function ur(e){const t=tj[e];return t||Ct(0,e),t}var di;function dv(){return di}function nj(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Nm(e,t){t&&(ur("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Ou(e){zu(e),e.drafts_.forEach(rj),e.drafts_=null}function zu(e){e===di&&(di=e.parent_)}function _m(e){return di=nj(di,e)}function rj(e){const t=e[at];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Em(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return e!==void 0&&e!==n?(n[at].modified_&&(Ou(t),Ct(4)),lr(e)&&(e=Ko(t,e),t.parent_||Go(t,e)),t.patches_&&ur("Patches").generateReplacementPatches_(n[at].base_,e,t.patches_,t.inversePatches_)):e=Ko(t,n,[]),Ou(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==lv?e:void 0}function Ko(e,t,n){if(Ca(t))return t;const r=t[at];if(!r)return Wo(t,(s,i)=>Pm(e,r,t,s,i,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return Go(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const s=r.copy_;let i=s,o=!1;r.type_===3&&(i=new Set(s),s.clear(),o=!0),Wo(i,(a,u)=>Pm(e,r,s,a,u,n,o)),Go(e,s,!1),n&&e.patches_&&ur("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Pm(e,t,n,r,s,i,o){if(Zr(s)){const a=i&&t&&t.type_!==3&&!Fu(t.assigned_,r)?i.concat(r):void 0,u=Ko(e,s,a);if(cv(n,r,u),Zr(u))e.canAutoFreeze_=!1;else return}else o&&n.add(s);if(lr(s)&&!Ca(s)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Ko(e,s),(!t||!t.scope_.parent_)&&typeof r!="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r)&&Go(e,s)}}function Go(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Pd(t,n)}function sj(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:dv(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let s=r,i=Ad;n&&(s=[r],i=fi);const{revoke:o,proxy:a}=Proxy.revocable(s,i);return r.draft_=a,r.revoke_=o,a}var Ad={get(e,t){if(t===at)return e;const n=Wn(e);if(!Fu(n,t))return ij(e,n,t);const r=n[t];return e.finalized_||!lr(r)?r:r===vl(e.base_,t)?(xl(e),e.copy_[t]=Uu(r,e)):r},has(e,t){return t in Wn(e)},ownKeys(e){return Reflect.ownKeys(Wn(e))},set(e,t,n){const r=fv(Wn(e),t);if(r!=null&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const s=vl(Wn(e),t),i=s==null?void 0:s[at];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(JT(n,s)&&(n!==void 0||Fu(e.base_,t)))return!0;xl(e),$u(e)}return e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty(e,t){return vl(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,xl(e),$u(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const n=Wn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){Ct(11)},getPrototypeOf(e){return Yr(e.base_)},setPrototypeOf(){Ct(12)}},fi={};Wo(Ad,(e,t)=>{fi[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});fi.deleteProperty=function(e,t){return fi.set.call(this,e,t,void 0)};fi.set=function(e,t,n){return Ad.set.call(this,e[0],t,n,e[0])};function vl(e,t){const n=e[at];return(n?Wn(n):e)[t]}function ij(e,t,n){var s;const r=fv(t,n);return r?"value"in r?r.value:(s=r.get)==null?void 0:s.call(e.draft_):void 0}function fv(e,t){if(!(t in e))return;let n=Yr(e);for(;n;){const r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Yr(n)}}function $u(e){e.modified_||(e.modified_=!0,e.parent_&&$u(e.parent_))}function xl(e){e.copy_||(e.copy_=Iu(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var oj=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,n,r)=>{if(typeof t=="function"&&typeof n!="function"){const i=n;n=t;const o=this;return function(u=i,...c){return o.produce(u,d=>n.call(this,d,...c))}}typeof n!="function"&&Ct(6),r!==void 0&&typeof r!="function"&&Ct(7);let s;if(lr(t)){const i=_m(this),o=Uu(t,void 0);let a=!0;try{s=n(o),a=!1}finally{a?Ou(i):zu(i)}return Nm(i,r),Em(s,i)}else if(!t||typeof t!="object"){if(s=n(t),s===void 0&&(s=t),s===lv&&(s=void 0),this.autoFreeze_&&Pd(s,!0),r){const i=[],o=[];ur("Patches").generateReplacementPatches_(t,s,i,o),r(i,o)}return s}else Ct(1,t)},this.produceWithPatches=(t,n)=>{if(typeof t=="function")return(o,...a)=>this.produceWithPatches(o,u=>t(u,...a));let r,s;return[this.produce(t,n,(o,a)=>{r=o,s=a}),r,s]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){lr(e)||Ct(8),Zr(e)&&(e=aj(e));const t=_m(this),n=Uu(e,void 0);return n[at].isManual_=!0,zu(t),n}finishDraft(e,t){const n=e&&e[at];(!n||!n.isManual_)&&Ct(9);const{scope_:r}=n;return Nm(r,t),Em(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const s=t[n];if(s.path.length===0&&s.op==="replace"){e=s.value;break}}n>-1&&(t=t.slice(n+1));const r=ur("Patches").applyPatches_;return Zr(e)?r(e,t):this.produce(e,s=>r(s,t))}};function Uu(e,t){const n=Ta(e)?ur("MapSet").proxyMap_(e,t):ja(e)?ur("MapSet").proxySet_(e,t):sj(e,t);return(t?t.scope_:dv()).drafts_.push(n),n}function aj(e){return Zr(e)||Ct(10,e),hv(e)}function hv(e){if(!lr(e)||Ca(e))return e;const t=e[at];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=Iu(e,t.scope_.immer_.useStrictShallowCopy_)}else n=Iu(e,!0);return Wo(n,(r,s)=>{cv(n,r,hv(s))}),t&&(t.finalized_=!1),n}var lt=new oj,lj=lt.produce;lt.produceWithPatches.bind(lt);lt.setAutoFreeze.bind(lt);lt.setUseStrictShallowCopy.bind(lt);lt.applyPatches.bind(lt);lt.createDraft.bind(lt);lt.finishDraft.bind(lt);const uj=e=>(t,n,r)=>(r.setState=(s,i,...o)=>{const a=typeof s=="function"?lj(s):s;return t(a,i,...o)},e(r.setState,n,r)),Dd=uj;class cj{constructor(){Ut(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.todos))throw new Error("Electron API not available");const r=await window.electronAPI.todos[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTodos(t,n){return this.makeRequest("getAll",t,n)}async createTodo(t){return this.makeRequest("create",t)}async updateTodo(t,n){return this.makeRequest("update",t,n)}async deleteTodo(t){return this.makeRequest("delete",t)}async updateTodoStatus(t,n){return this.makeRequest("updateStatus",t,n)}async toggleTodoCompletion(t,n){const r=n==="completed"?"pending":"completed";return this.updateTodoStatus(t,r)}isOverdue(t){return t.due_date?new Date(t.due_date)<new Date&&t.status!=="completed":!1}getPriorityWeight(t){return{very_low:1,low:2,medium:3,high:4,very_high:5}[t]||3}formatDueDate(t){const n=new Date(t),r=new Date,s=n.getTime()-r.getTime(),i=Math.ceil(s/(1e3*60*60*24));return i<0?`${Math.abs(i)} days overdue`:i===0?"Due today":i===1?"Due tomorrow":i<=7?`Due in ${i} days`:n.toLocaleDateString("en-US",{month:"short",day:"numeric",year:n.getFullYear()!==r.getFullYear()?"numeric":void 0})}}const Oe=new cj;class dj{constructor(){Ut(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.categories))throw new Error("Electron API not available");const r=await window.electronAPI.categories[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllCategories(){return this.makeRequest("getAll")}async createCategory(t){return this.makeRequest("create",t)}async updateCategory(t,n){return this.makeRequest("update",t,n)}async deleteCategory(t){return this.makeRequest("delete",t)}async reorderCategories(t){return this.makeRequest("reorder",t)}}const Wi=new dj;class fj{constructor(){Ut(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.tags))throw new Error("Electron API not available");const r=await window.electronAPI.tags[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTags(){return this.makeRequest("getAll")}async getTagSuggestions(t,n=10){return this.makeRequest("getSuggestions",t,n)}async getPopularTags(t=20){return this.makeRequest("getPopular",t)}async getRecentTags(t=10){return this.makeRequest("getRecent",t)}async getTagStats(t){return this.makeRequest("getStats",t)}async getSmartSuggestions(t="",n=10){try{if(t.trim())return await this.getTagSuggestions(t,n);{const[r,s]=await Promise.all([this.getPopularTags(Math.ceil(n*.7)),this.getRecentTags(Math.ceil(n*.3))]),i=[...r,...s];return Array.from(new Set(i)).slice(0,n)}}catch(r){return console.error("Error getting smart suggestions:",r),[]}}async getAutocompleteSuggestions(t,n=[],r=5){try{let s=[];return t.trim()?s=await this.getTagSuggestions(t,r*2):s=await this.getSmartSuggestions("",r*2),s.filter(o=>!n.includes(o)).slice(0,r)}catch(s){return console.error("Error getting autocomplete suggestions:",s),[]}}}const Ts=new fj,Am={status:[],priority:[],categoryId:[],tags:[],dueDateRange:{},searchQuery:"",sortBy:"position",sortOrder:"asc"},hj=()=>`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,mj=(e,t)=>e.filter(n=>{if(t.status.length>0&&!t.status.includes(n.status)||t.priority.length>0&&!t.priority.includes(n.priority)||t.categoryId.length>0&&(!n.category_id||!t.categoryId.includes(n.category_id))||t.tags.length>0&&!t.tags.some(s=>n.tags.includes(s)))return!1;if(t.dueDateRange.start||t.dueDateRange.end){if(!n.due_date)return!1;const r=new Date(n.due_date);if(t.dueDateRange.start&&r<t.dueDateRange.start||t.dueDateRange.end&&r>t.dueDateRange.end)return!1}if(t.searchQuery){const r=t.searchQuery.toLowerCase();if(![n.title,n.description||"",...n.tags].join(" ").toLowerCase().includes(r))return!1}return!0}),pj=(e,t,n)=>[...e].sort((s,i)=>{let o,a;switch(t){case"position":o=s.position,a=i.position;break;case"created_at":o=new Date(s.created_at),a=new Date(i.created_at);break;case"updated_at":o=new Date(s.updated_at),a=new Date(i.updated_at);break;case"due_date":o=s.due_date?new Date(s.due_date):new Date("9999-12-31"),a=i.due_date?new Date(i.due_date):new Date("9999-12-31");break;case"priority":const u={very_low:1,low:2,medium:3,high:4,very_high:5};o=u[s.priority],a=u[i.priority];break;case"title":o=s.title.toLowerCase(),a=i.title.toLowerCase();break;default:return 0}return o<a?n==="asc"?-1:1:o>a?n==="asc"?1:-1:0}),Md=Nd()(_d(Dd(Ed((e,t)=>({todos:[],categories:[],filters:Am,isLoading:!1,error:null,selectedTodos:[],draggedTodo:null,totalCount:0,completedCount:0,pendingCount:0,loadTodos:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const r=await Oe.getAll("current-session");e(s=>{var i,o,a;s.todos=r.todos||[],s.totalCount=((i=r.todos)==null?void 0:i.length)||0,s.completedCount=((o=r.todos)==null?void 0:o.filter(u=>u.status==="completed").length)||0,s.pendingCount=((a=r.todos)==null?void 0:a.filter(u=>u.status==="pending").length)||0,s.isLoading=!1})}catch(n){e(r=>{r.error=n instanceof Error?n.message:"Failed to load todos",r.isLoading=!1})}},createTodo:async n=>{const r=hj(),s={id:r,...n,created_at:new Date,updated_at:new Date,is_deleted:!1};e(i=>{i.todos.unshift(s),i.totalCount+=1,s.status==="pending"&&(i.pendingCount+=1)});try{const o=await Oe.create("current-session",n);return e(a=>{const u=a.todos.findIndex(c=>c.id===r);u!==-1&&(a.todos[u]=o.todo)}),o.todo.id}catch(i){throw e(o=>{o.todos=o.todos.filter(a=>a.id!==r),o.totalCount-=1,s.status==="pending"&&(o.pendingCount-=1),o.error=i instanceof Error?i.message:"Failed to create todo"}),i}},updateTodo:async(n,r)=>{const s=t().todos.find(i=>i.id===n);if(s){e(i=>{const o=i.todos.findIndex(a=>a.id===n);o!==-1&&(i.todos[o]={...i.todos[o],...r,updated_at:new Date})});try{const o=await Oe.update("current-session",n,r);e(a=>{const u=a.todos.findIndex(c=>c.id===n);u!==-1&&(a.todos[u]=o.todo)})}catch(i){throw e(o=>{const a=o.todos.findIndex(u=>u.id===n);a!==-1&&(o.todos[a]=s),o.error=i instanceof Error?i.message:"Failed to update todo"}),i}}},deleteTodo:async n=>{const r=t().todos.find(s=>s.id===n);if(r){e(s=>{s.todos=s.todos.filter(i=>i.id!==n),s.totalCount-=1,r.status==="completed"?s.completedCount-=1:r.status==="pending"&&(s.pendingCount-=1)});try{await Oe.delete("current-session",n)}catch(s){throw e(i=>{i.todos.push(r),i.totalCount+=1,r.status==="completed"?i.completedCount+=1:r.status==="pending"&&(i.pendingCount+=1),i.error=s instanceof Error?s.message:"Failed to delete todo"}),s}}},toggleTodoComplete:async n=>{const r=t().todos.find(o=>o.id===n);if(!r)return;const s=r.status==="completed"?"pending":"completed",i={status:s,completed_at:s==="completed"?new Date:void 0};await t().updateTodo(n,i)},reorderTodos:async(n,r,s)=>{console.log("Reorder todos:",{sourceIndex:n,destinationIndex:r,categoryId:s})},createCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await Wi.createCategory(n);return e(s=>{s.categories.push(r),s.isLoading=!1}),r.id}catch(r){const s=r instanceof Error?r.message:"Failed to create category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},updateCategory:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await Wi.updateCategory(n,r);e(i=>{const o=i.categories.findIndex(a=>a.id===n);o!==-1&&(i.categories[o]=s),i.isLoading=!1})}catch(s){const i=s instanceof Error?s.message:"Failed to update category";throw e(o=>{o.error=i,o.isLoading=!1}),s}},deleteCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{await Wi.deleteCategory(n),e(r=>{r.categories=r.categories.filter(s=>s.id!==n),r.todos.forEach(s=>{s.category_id===n&&(s.category_id=void 0)}),r.isLoading=!1})}catch(r){const s=r instanceof Error?r.message:"Failed to delete category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},loadCategories:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const n=await Wi.getAllCategories();e(r=>{r.categories=n,r.isLoading=!1})}catch(n){const r=n instanceof Error?n.message:"Failed to load categories";throw e(s=>{s.error=r,s.isLoading=!1}),n}},setFilters:n=>{e(r=>{r.filters={...r.filters,...n}})},clearFilters:()=>{e(n=>{n.filters=Am})},setSearchQuery:n=>{e(r=>{r.filters.searchQuery=n})},setSortConfig:(n,r)=>{e(s=>{s.filters.sortBy=n,s.filters.sortOrder=r})},getAllTags:()=>{const{todos:n}=t(),r=new Set;return n.forEach(s=>{s.tags.forEach(i=>r.add(i))}),Array.from(r).sort()},getTagsWithStats:async()=>{try{return await Ts.getAllTags()}catch(n){return console.error("Error getting tags with stats:",n),[]}},getTagSuggestions:async(n,r)=>{try{return await Ts.getTagSuggestions(n,r)}catch(s){return console.error("Error getting tag suggestions:",s),[]}},getPopularTags:async n=>{try{return await Ts.getPopularTags(n)}catch(r){return console.error("Error getting popular tags:",r),[]}},getRecentTags:async n=>{try{return await Ts.getRecentTags(n)}catch(r){return console.error("Error getting recent tags:",r),[]}},bulkUpdateTags:async(n,r)=>{const{todos:s}=t(),i=s.filter(o=>o.tags.includes(n));e(o=>{o.isLoading=!0,o.error=null});try{await Promise.all(i.map(o=>{const a=o.tags.map(u=>u===n?r:u);return Oe.updateTodo(o.id,{tags:a})})),e(o=>{o.todos.forEach(a=>{a.tags.includes(n)&&(a.tags=a.tags.map(u=>u===n?r:u))}),o.isLoading=!1})}catch(o){const a=o instanceof Error?o.message:"Failed to update tags";throw e(u=>{u.error=a,u.isLoading=!1}),o}},deleteTag:async n=>{const{todos:r}=t(),s=r.filter(i=>i.tags.includes(n));e(i=>{i.isLoading=!0,i.error=null});try{await Promise.all(s.map(i=>{const o=i.tags.filter(a=>a!==n);return Oe.updateTodo(i.id,{tags:o})})),e(i=>{i.todos.forEach(o=>{o.tags=o.tags.filter(a=>a!==n)}),i.filters.tags=i.filters.tags.filter(o=>o!==n),i.isLoading=!1})}catch(i){const o=i instanceof Error?i.message:"Failed to delete tag";throw e(a=>{a.error=o,a.isLoading=!1}),i}},selectTodo:n=>{e(r=>{r.selectedTodos.includes(n)||r.selectedTodos.push(n)})},deselectTodo:n=>{e(r=>{r.selectedTodos=r.selectedTodos.filter(s=>s!==n)})},selectAllTodos:()=>{e(n=>{const r=t().getFilteredTodos();n.selectedTodos=r.map(s=>s.id)})},clearSelection:()=>{e(n=>{n.selectedTodos=[]})},toggleTodoSelection:n=>{const{selectedTodos:r}=t();r.includes(n)?t().deselectTodo(n):t().selectTodo(n)},setDraggedTodo:n=>{e(r=>{r.draggedTodo=n})},getFilteredTodos:()=>{const{todos:n,filters:r}=t(),s=mj(n,r);return pj(s,r.sortBy,r.sortOrder)},getTodosByCategory:n=>{const{todos:r}=t();return r.filter(s=>s.category_id===n)},getCompletedTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="completed")},getPendingTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="pending")},getOverdueTodos:()=>{const{todos:n}=t(),r=new Date;return n.filter(s=>s.due_date&&new Date(s.due_date)<r&&s.status!=="completed")},getTodoStats:()=>{const{todos:n}=t(),r=new Date,s=n.filter(i=>i.due_date&&new Date(i.due_date)<r&&i.status!=="completed").length;return{total:n.length,completed:n.filter(i=>i.status==="completed").length,pending:n.filter(i=>i.status==="pending").length,overdue:s}}}),{name:"todo-store",partialize:e=>({filters:e.filters,selectedTodos:e.selectedTodos})})))),Dm=e=>e?new Date>=e:!0,gj=e=>{if(!e)return 0;const t=new Date,n=e.getTime()-t.getTime();return Math.max(0,n)};Nd()(_d(Dd(Ed((e,t)=>({user:null,profile:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,sessionId:null,sessionExpiry:null,lastActivity:null,login:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.login(n,r);e(i=>{i.user=s.user,i.session=s.session,i.sessionId=s.session.session_id,i.sessionExpiry=new Date(s.session.expires_at),i.lastActivity=new Date,i.isAuthenticated=!0,i.isLoading=!1}),await t().loadProfile()}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Login failed",i.isLoading=!1,i.isAuthenticated=!1}),s}},logout:async()=>{e(n=>{n.isLoading=!0});try{const{sessionId:n}=t();n&&await fe.logout(n)}catch(n){console.error("Logout error:",n)}finally{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.isLoading=!1,n.error=null})}},register:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await fe.register(n);e(s=>{s.user=r.user,s.session=r.session,s.sessionId=r.session.session_id,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isAuthenticated=!0,s.isLoading=!1}),await t().loadProfile()}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Registration failed",s.isLoading=!1,s.isAuthenticated=!1}),r}},refreshSession:async()=>{const{sessionId:n}=t();if(!n)throw new Error("No active session to refresh");e(r=>{r.isLoading=!0});try{const r=await fe.refreshSession(n);e(s=>{s.session=r.session,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isLoading=!1})}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Session refresh failed",s.isLoading=!1}),await t().logout(),r}},updateProfile:async n=>{const{user:r}=t();if(!r)throw new Error("No authenticated user");e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.updateProfile(r.id,n);e(i=>{i.profile=s.profile,i.isLoading=!1})}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Profile update failed",i.isLoading=!1}),s}},loadProfile:async()=>{const{user:n}=t();if(n)try{const r=await fe.getProfile(n.id);e(s=>{s.profile=r})}catch(r){console.error("Failed to load profile:",r)}},validateSession:async()=>{const{sessionId:n,sessionExpiry:r}=t();if(!n||!r)return!1;if(Dm(r))return await t().logout(),!1;try{return await fe.validateSession(n)?(e(i=>{i.lastActivity=new Date}),!0):(await t().logout(),!1)}catch(s){return console.error("Session validation error:",s),await t().logout(),!1}},extendSession:async()=>{const{sessionId:n}=t();if(n)try{const r=await fe.extendSession(n);e(s=>{s.sessionExpiry=new Date(r.expiresAt),s.lastActivity=new Date})}catch(r){console.error("Failed to extend session:",r)}},clearSession:()=>{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.error=null})},clearError:()=>{e(n=>{n.error=null})},setError:n=>{e(r=>{r.error=n})},isSessionValid:()=>{const{sessionExpiry:n}=t();return!Dm(n)},getSessionTimeRemaining:()=>{const{sessionExpiry:n}=t();return gj(n)},getUserDisplayName:()=>{const{user:n,profile:r}=t();return r!=null&&r.full_name?r.full_name:n!=null&&n.username?n.username:"User"}}),{name:"auth-store",partialize:e=>({user:e.user,profile:e.profile,session:e.session,sessionId:e.sessionId,sessionExpiry:e.sessionExpiry,lastActivity:e.lastActivity,isAuthenticated:e.isAuthenticated})}))));const yj=()=>`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,vj=e=>{const t=["light","dark","frutiger-aero"],n=t.indexOf(e);return t[(n+1)%t.length]},xj=Nd()(_d(Dd(Ed((e,t)=>({theme:"frutiger-aero",sidebarOpen:!0,sidebarWidth:280,activeModal:null,modalData:null,notifications:[],globalLoading:!1,loadingStates:{},currentView:"todos",previousView:null,todoViewMode:"list",showCompletedTodos:!0,autoSaveEnabled:!0,compactMode:!1,showNotifications:!0,soundEnabled:!0,setTheme:n=>{e(r=>{r.theme=n}),document.documentElement.setAttribute("data-theme",n),localStorage.setItem("fa-theme",n)},toggleTheme:()=>{const n=t().theme,r=vj(n);t().setTheme(r)},initializeTheme:()=>{try{const n=localStorage.getItem("fa-theme");if(n&&["light","dark","frutiger-aero"].includes(n))t().setTheme(n);else{const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";t().setTheme(r)}}catch(n){console.error("Error initializing theme:",n),t().setTheme("light")}},toggleSidebar:()=>{e(n=>{n.sidebarOpen=!n.sidebarOpen})},setSidebarOpen:n=>{e(r=>{r.sidebarOpen=n})},setSidebarWidth:n=>{e(r=>{r.sidebarWidth=Math.max(200,Math.min(400,n))})},openModal:(n,r=null)=>{e(s=>{s.activeModal=n,s.modalData=r})},closeModal:()=>{e(n=>{n.activeModal=null,n.modalData=null})},addNotification:n=>{const r=yj(),s={id:r,...n,createdAt:new Date};return e(i=>{i.notifications.push(s)}),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(r)},s.duration),r},removeNotification:n=>{e(r=>{r.notifications=r.notifications.filter(s=>s.id!==n)})},clearNotifications:()=>{e(n=>{n.notifications=[]})},setGlobalLoading:n=>{e(r=>{r.globalLoading=n})},setLoadingState:(n,r)=>{e(s=>{r?s.loadingStates[n]=!0:delete s.loadingStates[n]})},clearLoadingState:n=>{e(r=>{delete r.loadingStates[n]})},setCurrentView:n=>{e(r=>{r.previousView=r.currentView,r.currentView=n})},setTodoViewMode:n=>{e(r=>{r.todoViewMode=n})},toggleCompletedTodos:()=>{e(n=>{n.showCompletedTodos=!n.showCompletedTodos})},setAutoSave:n=>{e(r=>{r.autoSaveEnabled=n})},setCompactMode:n=>{e(r=>{r.compactMode=n})},setShowNotifications:n=>{e(r=>{r.showNotifications=n})},setSoundEnabled:n=>{e(r=>{r.soundEnabled=n})},getActiveNotifications:()=>{const{notifications:n,showNotifications:r}=t();return r?n:[]},isLoading:n=>{const{globalLoading:r,loadingStates:s}=t();return n?s[n]||!1:r||Object.keys(s).length>0}}),{name:"ui-store",partialize:e=>({theme:e.theme,sidebarOpen:e.sidebarOpen,sidebarWidth:e.sidebarWidth,currentView:e.currentView,todoViewMode:e.todoViewMode,showCompletedTodos:e.showCompletedTodos,autoSaveEnabled:e.autoSaveEnabled,compactMode:e.compactMode,showNotifications:e.showNotifications,soundEnabled:e.soundEnabled})})))),wj=()=>{try{const e=useUIStore.getState().initializeTheme;if(e)e();else{const t=localStorage.getItem("fa-theme");t?document.documentElement.setAttribute("data-theme",t):document.documentElement.setAttribute("data-theme","light")}console.log("Stores initialized successfully")}catch(e){console.error("Error initializing stores:",e)}};function Sj(e,t){const[n,r]=w.useState(e);return w.useEffect(()=>{const s=setTimeout(()=>{r(e)},t);return()=>{clearTimeout(s)}},[e,t]),n}function bj(e,t,n=[]){const r=w.useRef(),s=w.useRef(e);w.useEffect(()=>{s.current=e},[e,...n]);const i=w.useCallback((...o)=>{r.current&&clearTimeout(r.current),r.current=setTimeout(()=>{s.current(...o)},t)},[t]);return w.useEffect(()=>()=>{r.current&&clearTimeout(r.current)},[]),i}const kj=({value:e,onChange:t,onFilterToggle:n,placeholder:r="Search todos...",showFilterButton:s=!0,isFilterActive:i=!1,suggestions:o=[],className:a=""})=>{const[u,c]=w.useState(!1),[d,h]=w.useState(!1),[f,g]=w.useState(-1),v=w.useRef(null),x=w.useRef(null),S=bj(t,300,[t]),p=o.filter(T=>T.toLowerCase().includes(e.toLowerCase())&&T!==e).slice(0,5),m=T=>{const E=T.target.value;t(E),S(E),g(-1),h(E.length>0&&p.length>0)},y=()=>{var T;t(""),h(!1),(T=v.current)==null||T.focus()},k=T=>{var E;t(T),h(!1),g(-1),(E=v.current)==null||E.focus()},j=T=>{var E,A;if(!d||p.length===0){T.key==="Escape"&&((E=v.current)==null||E.blur());return}switch(T.key){case"ArrowDown":T.preventDefault(),g(R=>R<p.length-1?R+1:0);break;case"ArrowUp":T.preventDefault(),g(R=>R>0?R-1:p.length-1);break;case"Enter":T.preventDefault(),f>=0&&k(p[f]);break;case"Escape":h(!1),g(-1),(A=v.current)==null||A.blur();break}},C=()=>{c(!0),e.length>0&&p.length>0&&h(!0)},_=()=>{c(!1),setTimeout(()=>{h(!1),g(-1)},150)};return w.useEffect(()=>{const T=E=>{var A;(E.metaKey||E.ctrlKey)&&E.key==="k"&&(E.preventDefault(),(A=v.current)==null||A.focus())};return document.addEventListener("keydown",T),()=>document.removeEventListener("keydown",T)},[]),l.jsxs("div",{className:`relative ${a}`,children:[l.jsx("div",{className:`fa-glass-panel relative transition-all duration-300 ${u?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"flex-shrink-0 pl-4",children:l.jsx(Vu,{className:`w-5 h-5 transition-colors duration-200 ${u?"text-fa-blue-500":"text-fa-gray-400"}`})}),l.jsx("input",{ref:v,type:"text",value:e,onChange:m,onFocus:C,onBlur:_,onKeyDown:j,placeholder:r,className:"flex-1 bg-transparent px-4 py-3 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"}),!u&&!e&&l.jsx("div",{className:"flex-shrink-0 pr-2",children:l.jsxs("div",{className:"flex items-center space-x-1 text-fa-gray-400 text-sm",children:[l.jsx(Gk,{className:"w-3 h-3"}),l.jsx("span",{children:"K"})]})}),e&&l.jsx(P.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y,className:"flex-shrink-0 p-1 mx-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:l.jsx(wt,{className:"w-4 h-4"})}),s&&l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:n,className:`flex-shrink-0 p-2 mx-2 rounded-lg transition-all duration-200 ${i?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,children:l.jsx(Qk,{className:"w-4 h-4"})})]})}),l.jsx(ae,{children:d&&p.length>0&&l.jsx(P.div,{ref:x,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 right-0 mt-2 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto",children:p.map((T,E)=>l.jsx(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>k(T),className:`w-full text-left px-4 py-3 transition-colors duration-150 ${E===f?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"} ${E===0?"rounded-t-xl":""} ${E===p.length-1?"rounded-b-xl":""}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(Vu,{className:"w-4 h-4 mr-3 text-fa-gray-400"}),l.jsx("span",{children:T})]})},T))})})]})},Bu={very_high:{label:"Very High",color:"text-red-600",bgColor:"bg-red-100",borderColor:"border-red-300",textColor:"text-red-800",icon:jd,weight:5,gradient:"from-red-500 to-red-600",glowColor:"shadow-red-500/50"},high:{label:"High",color:"text-orange-600",bgColor:"bg-orange-100",borderColor:"border-orange-300",textColor:"text-orange-800",icon:Wy,weight:4,gradient:"from-orange-500 to-orange-600",glowColor:"shadow-orange-500/30"},medium:{label:"Medium",color:"text-yellow-600",bgColor:"bg-yellow-100",borderColor:"border-yellow-300",textColor:"text-yellow-800",icon:Xy,weight:3,gradient:"from-yellow-500 to-yellow-600"},low:{label:"Low",color:"text-blue-600",bgColor:"bg-blue-100",borderColor:"border-blue-300",textColor:"text-blue-800",icon:Hy,weight:2,gradient:"from-blue-500 to-blue-600"},very_low:{label:"Very Low",color:"text-gray-500",bgColor:"bg-gray-100",borderColor:"border-gray-300",textColor:"text-gray-700",icon:Zk,weight:1,gradient:"from-gray-400 to-gray-500"}};function Vn(e){return Bu[e]||Bu.medium}function Mm(e){return Vn(e).color}function wl(e){return Vn(e).bgColor}function Sl(e){return Vn(e).borderColor}function cn(e){return Vn(e).textColor}function Tj(e){return Vn(e).icon}function On(e){return Vn(e).label}function Rm(e){return e==="high"||e==="very_high"}function co(e){return e==="very_high"}function Ki(e){return Vn(e).gradient||""}function Lm(e){return Vn(e).glowColor||""}function jj(){return Object.entries(Bu).map(([e,t])=>({value:e,label:t.label,config:t}))}function Cj(e){switch(e){case"very_high":return"h-2";case"high":return"h-1.5";case"medium":return"h-1";case"low":return"h-0.5";case"very_low":return"h-0.5";default:return"h-1"}}function Gi(e){return e==="very_high"?"animate-pulse":""}const qo=({priority:e,variant:t="icon",size:n="md",showLabel:r=!1,className:s="",animated:i=!0})=>{const o=Tj(e),a={sm:"w-3 h-3 text-xs",md:"w-4 h-4 text-sm",lg:"w-5 h-5 text-base"},u={sm:"px-1.5 py-0.5 text-xs",md:"px-2 py-1 text-sm",lg:"px-3 py-1.5 text-base"},c=a[n],d=u[n];return t==="icon"?l.jsxs(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${c} ${Mm(e)} ${Gi(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="badge"?l.jsxs(P.div,{className:`inline-flex items-center rounded-full ${d} ${wl(e)} ${Sl(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="bar"?l.jsx(P.div,{className:`w-full ${Cj(e)} bg-gradient-to-r ${Ki(e)} rounded-full ${s} ${Gi(e)}`,initial:i?{scaleX:0}:void 0,animate:i?{scaleX:1}:void 0,transition:{duration:.3}}):t==="chip"?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} ${wl(e)} ${Sl(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]}):t==="full"?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Ki(e)} text-white shadow-lg ${Lm(e)} ${s} ${Gi(e)}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx(o,{className:`${a.sm} text-white`}),l.jsx("span",{className:"ml-1 text-white font-medium",children:On(e)})]}):t==="minimal"?l.jsxs(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[l.jsx("div",{className:`w-2 h-2 rounded-full bg-gradient-to-r ${Ki(e)} ${Gi(e)}`}),r&&l.jsx("span",{className:`ml-2 ${cn(e)} text-sm font-medium`,children:On(e)})]}):t==="glow"?l.jsx(P.div,{className:`inline-flex items-center rounded-lg ${d} ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:co(e)?l.jsxs(P.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Ki(e)} text-white shadow-lg ${Lm(e)} animate-pulse`,animate:{boxShadow:["0 0 20px rgba(239, 68, 68, 0.5)","0 0 30px rgba(239, 68, 68, 0.8)","0 0 20px rgba(239, 68, 68, 0.5)"]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:[l.jsx(o,{className:`${a.sm} text-white`}),r&&l.jsx("span",{className:"ml-1 text-white font-bold",children:On(e)})]}):l.jsxs("div",{className:`inline-flex items-center rounded-lg ${d} ${wl(e)} ${Sl(e)} border`,children:[l.jsx(o,{className:`${a.sm} ${cn(e)}`}),r&&l.jsx("span",{className:`ml-1 ${cn(e)} font-medium`,children:On(e)})]})}):l.jsx(P.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:l.jsx(o,{className:`${c} ${Mm(e)}`})})},Nj={pending:"Pending",in_progress:"In Progress",completed:"Completed",archived:"Archived",cancelled:"Cancelled"},_j=[{value:"all",label:"All"},{value:"overdue",label:"Overdue"},{value:"today",label:"Due Today"},{value:"tomorrow",label:"Due Tomorrow"},{value:"this_week",label:"This Week"},{value:"this_month",label:"This Month"},{value:"no_due_date",label:"No Due Date"}],Ej=({isOpen:e,onClose:t,filters:n,onUpdateFilter:r,onApplyPreset:s,onClearFilters:i,presets:o,activePreset:a,filterOptions:u,filterSummary:c})=>{const[d,h]=w.useState(new Set(["presets","status","priority","categories"])),f=p=>{h(m=>{const y=new Set(m);return y.has(p)?y.delete(p):y.add(p),y})},g=p=>{const m=n.status.includes(p)?n.status.filter(y=>y!==p):[...n.status,p];r("status",m)},v=p=>{const m=n.priority.includes(p)?n.priority.filter(y=>y!==p):[...n.priority,p];r("priority",m)},x=p=>{const m=n.tags.includes(p)?n.tags.filter(y=>y!==p):[...n.tags,p];r("tags",m)},S=p=>{const m=n.category.includes(p)?n.category.filter(y=>y!==p):[...n.category,p];r("category",m)};return e?l.jsx(ae,{children:l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t,children:l.jsxs(P.div,{initial:{opacity:0,x:300},animate:{opacity:1,x:0},exit:{opacity:0,x:300},transition:{type:"spring",damping:25,stiffness:300},className:"absolute right-0 top-0 h-full w-96 fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto",onClick:p=>p.stopPropagation(),children:[l.jsxs("div",{className:"sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("h2",{className:"fa-heading-2",children:"Filters"}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:l.jsx(wt,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"mt-4 p-3 bg-fa-blue-50 rounded-lg",children:l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"text-fa-gray-600",children:["Showing ",c.totalFiltered," of ",c.totalOriginal," tasks"]}),c.hasActiveFilters&&l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:i,className:"flex items-center space-x-1 text-fa-blue-600 hover:text-fa-blue-700",children:[l.jsx(nT,{className:"w-3 h-3"}),l.jsx("span",{children:"Clear"})]})]})})]}),l.jsxs("div",{className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("presets"),className:"flex items-center justify-between w-full text-left",children:[l.jsx("h3",{className:"fa-heading-3",children:"Quick Filters"}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("presets")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("presets")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:o.map(p=>l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s(p.id),className:`w-full text-left p-3 rounded-lg transition-all duration-200 ${a===p.id?"bg-fa-blue-100 border border-fa-blue-300 text-fa-blue-800":"bg-fa-white-glass hover:bg-fa-gray-50 border border-fa-gray-200"}`,children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("span",{className:"text-lg",children:p.icon}),l.jsx("div",{children:l.jsx("div",{className:"font-medium",children:p.name})})]})},p.id))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("status"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Hn,{className:"w-4 h-4 mr-2"}),"Status"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("status")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("status")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.statuses.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>g(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.status.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.status.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ms,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:Nj[p]})]},p))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("priority"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Xy,{className:"w-4 h-4 mr-2"}),"Priority"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("priority")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("priority")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.priorities.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>v(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.priority.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.priority.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ms,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx(qo,{priority:p,variant:"icon",size:"sm",showLabel:!0,animated:!1})]},p))})})]}),l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("dueDate"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Ln,{className:"w-4 h-4 mr-2"}),"Due Date"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("dueDate")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("dueDate")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:_j.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r("dueDate",p.value),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.dueDate===p.value?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.dueDate===p.value?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ms,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:p.label})]},p.value))})})]}),u.categories.length>0&&l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("categories"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(Jt,{className:"w-4 h-4 mr-2"}),"Categories"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("categories")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("categories")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.categories.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>S(p.id),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.category.includes(p.id)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.category.includes(p.id)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ms,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:p.color}}),l.jsx("span",{children:p.name})]},p.id))})})]}),u.tags.length>0&&l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>f("tags"),className:"flex items-center justify-between w-full text-left",children:[l.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[l.jsx(hr,{className:"w-4 h-4 mr-2"}),"Tags"]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d.has("tags")?"rotate-180":""}`})]}),l.jsx(ae,{children:d.has("tags")&&l.jsx(P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:u.tags.map(p=>l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>x(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.tags.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.tags.includes(p)?l.jsx(Hn,{className:"w-4 h-4 text-fa-blue-600"}):l.jsx(ms,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:p})]},p))})})]})]})]})})}):null},Vm={title:"Title",status:"Status",priority:"Priority",due_date:"Due Date",created_at:"Created",updated_at:"Updated",position:"Position",estimated_duration:"Duration"},Pj=({sortConfig:e,onUpdateSort:t,onApplyPreset:n,onToggleSortOrder:r,presets:s,activePreset:i,sortDescription:o,className:a=""})=>{var p;const[u,c]=w.useState(!1),[d,h]=w.useState(!1),f=w.useRef(null),g=w.useRef(null);w.useEffect(()=>{const m=y=>{f.current&&!f.current.contains(y.target)&&c(!1),g.current&&!g.current.contains(y.target)&&h(!1)};return document.addEventListener("mousedown",m),()=>document.removeEventListener("mousedown",m)},[]);const v=m=>{t(m),c(!1)},x=m=>{n(m),h(!1)},S=()=>{switch(e.order){case"asc":return l.jsx(Wy,{className:"w-4 h-4"});case"desc":return l.jsx(Hy,{className:"w-4 h-4"});default:return l.jsx(Wk,{className:"w-4 h-4"})}};return l.jsxs("div",{className:`flex items-center space-x-2 ${a}`,children:[l.jsxs("div",{className:"relative",ref:g,children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>h(!d),className:`fa-button-glass px-3 py-2 flex items-center space-x-2 ${i?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:[l.jsx("span",{className:"text-sm font-medium",children:i?(p=s.find(m=>m.id===i))==null?void 0:p.name:"Sort by"}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${d?"rotate-180":""}`})]}),l.jsx(ae,{children:d&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto",children:l.jsxs("div",{className:"p-2",children:[l.jsx("div",{className:"px-3 py-2 text-xs font-medium text-fa-gray-500 uppercase tracking-wide",children:"Quick Sort Options"}),s.map(m=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>x(m.id),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${i===m.id?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("span",{className:"text-lg",children:m.icon}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium",children:m.name}),m.description&&l.jsx("div",{className:"text-xs text-fa-gray-500",children:m.description})]})]}),i===m.id&&l.jsx(Ho,{className:"w-4 h-4 text-fa-blue-600"})]},m.id))]})})})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsxs("div",{className:"relative",ref:f,children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>c(!u),className:"fa-button-glass px-3 py-2 flex items-center space-x-2",children:[l.jsx("span",{className:"text-sm",children:Vm[e.field]}),l.jsx(Kt,{className:`w-4 h-4 transition-transform ${u?"rotate-180":""}`})]}),l.jsx(ae,{children:u&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-48 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:l.jsx("div",{className:"p-2",children:Object.entries(Vm).map(([m,y])=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>v(m),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${e.field===m?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsx("span",{children:y}),e.field===m&&l.jsx(Ho,{className:"w-4 h-4 text-fa-blue-600"})]},m))})})})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:r,className:"fa-button-glass p-2 flex items-center justify-center",title:`Sort ${e.order==="asc"?"descending":"ascending"}`,children:S()})]}),l.jsx("div",{className:"hidden md:block",children:l.jsx("span",{className:"text-sm text-fa-gray-500",children:o})})]})},mv=({isOpen:e,onClose:t,onConfirm:n,title:r,message:s,confirmText:i="Confirm",cancelText:o="Cancel",variant:a="danger",isLoading:u=!1})=>{const d=(()=>{switch(a){case"danger":return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"};case"warning":return{icon:"text-fa-warning",confirmButton:"bg-fa-warning hover:bg-yellow-600 text-white",border:"border-fa-warning-border"};case"info":return{icon:"text-fa-info",confirmButton:"bg-fa-info hover:bg-blue-600 text-white",border:"border-fa-info-border"};default:return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"}}})();return l.jsx(ae,{children:e&&l.jsxs(l.Fragment,{children:[l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t}),l.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:l.jsxs(P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted max-w-md w-full p-6 ${d.border}`,onClick:h=>h.stopPropagation(),children:[l.jsxs("div",{className:"flex items-start justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:`p-2 rounded-full bg-fa-white-glass ${d.icon}`,children:l.jsx(jd,{className:"w-5 h-5"})}),l.jsx("h3",{className:"fa-heading-3 text-fa-gray-800",children:r})]}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-1 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:u,children:l.jsx(wt,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"mb-6",children:l.jsx("p",{className:"fa-body text-fa-gray-600",children:s})}),l.jsxs("div",{className:"flex items-center justify-end space-x-3",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,disabled:u,className:"fa-button-glass px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:o}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:n,disabled:u,className:`px-4 py-2 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${d.confirmButton}`,children:u?l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),l.jsx("span",{children:"Processing..."})]}):i})]})]})})]})})};var bi=e=>e.type==="checkbox",Yn=e=>e instanceof Date,Be=e=>e==null;const pv=e=>typeof e=="object";var be=e=>!Be(e)&&!Array.isArray(e)&&pv(e)&&!Yn(e),gv=e=>be(e)&&e.target?bi(e.target)?e.target.checked:e.target.value:e,Aj=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,yv=(e,t)=>e.has(Aj(t)),Dj=e=>{const t=e.constructor&&e.constructor.prototype;return be(t)&&t.hasOwnProperty("isPrototypeOf")},Rd=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Ce(e){let t;const n=Array.isArray(e),r=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(Rd&&(e instanceof Blob||r))&&(n||be(e)))if(t=n?[]:Object.create(Object.getPrototypeOf(e)),!n&&!Dj(e))t=e;else for(const s in e)e.hasOwnProperty(s)&&(t[s]=Ce(e[s]));else return e;return t}var Na=e=>/^\w*$/.test(e),we=e=>e===void 0,Ld=e=>Array.isArray(e)?e.filter(Boolean):[],Vd=e=>Ld(e.replace(/["|']|\]/g,"").split(/\.|\[/)),z=(e,t,n)=>{if(!t||!be(e))return n;const r=(Na(t)?[t]:Vd(t)).reduce((s,i)=>Be(s)?s:s[i],e);return we(r)||r===e?we(e[t])?n:e[t]:r},tt=e=>typeof e=="boolean",se=(e,t,n)=>{let r=-1;const s=Na(t)?[t]:Vd(t),i=s.length,o=i-1;for(;++r<i;){const a=s[r];let u=n;if(r!==o){const c=e[a];u=be(c)||Array.isArray(c)?c:isNaN(+s[r+1])?{}:[]}if(a==="__proto__"||a==="constructor"||a==="prototype")return;e[a]=u,e=e[a]}};const Qo={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Nt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Bt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},vv=Z.createContext(null);vv.displayName="HookFormContext";const Fd=()=>Z.useContext(vv);var xv=(e,t,n,r=!0)=>{const s={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(s,i,{get:()=>{const o=i;return t._proxyFormState[o]!==Nt.all&&(t._proxyFormState[o]=!r||Nt.all),n&&(n[o]=!0),e[o]}});return s};const Id=typeof window<"u"?Z.useLayoutEffect:Z.useEffect;function Mj(e){const t=Fd(),{control:n=t.control,disabled:r,name:s,exact:i}=e||{},[o,a]=Z.useState(n._formState),u=Z.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Id(()=>n._subscribe({name:s,formState:u.current,exact:i,callback:c=>{!r&&a({...n._formState,...c})}}),[s,r,i]),Z.useEffect(()=>{u.current.isValid&&n._setValid(!0)},[n]),Z.useMemo(()=>xv(o,n,u.current,!1),[o,n])}var Lt=e=>typeof e=="string",wv=(e,t,n,r,s)=>Lt(e)?(r&&t.watch.add(e),z(n,e,s)):Array.isArray(e)?e.map(i=>(r&&t.watch.add(i),z(n,i))):(r&&(t.watchAll=!0),n),Hu=e=>Be(e)||!pv(e);function Gt(e,t,n=new WeakSet){if(Hu(e)||Hu(t))return e===t;if(Yn(e)&&Yn(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const i of r){const o=e[i];if(!s.includes(i))return!1;if(i!=="ref"){const a=t[i];if(Yn(o)&&Yn(a)||be(o)&&be(a)||Array.isArray(o)&&Array.isArray(a)?!Gt(o,a,n):o!==a)return!1}}return!0}function Rj(e){const t=Fd(),{control:n=t.control,name:r,defaultValue:s,disabled:i,exact:o,compute:a}=e||{},u=Z.useRef(s),c=Z.useRef(a),d=Z.useRef(void 0);c.current=a;const h=Z.useMemo(()=>n._getWatch(r,u.current),[n,r]),[f,g]=Z.useState(c.current?c.current(h):h);return Id(()=>n._subscribe({name:r,formState:{values:!0},exact:o,callback:v=>{if(!i){const x=wv(r,n._names,v.values||n._formValues,!1,u.current);if(c.current){const S=c.current(x);Gt(S,d.current)||(g(S),d.current=S)}else g(x)}}}),[n,i,r,o]),Z.useEffect(()=>n._removeUnmounted()),f}function Lj(e){const t=Fd(),{name:n,disabled:r,control:s=t.control,shouldUnregister:i,defaultValue:o}=e,a=yv(s._names.array,n),u=Z.useMemo(()=>z(s._formValues,n,z(s._defaultValues,n,o)),[s,n,o]),c=Rj({control:s,name:n,defaultValue:u,exact:!0}),d=Mj({control:s,name:n,exact:!0}),h=Z.useRef(e),f=Z.useRef(s.register(n,{...e.rules,value:c,...tt(e.disabled)?{disabled:e.disabled}:{}}));h.current=e;const g=Z.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!z(d.errors,n)},isDirty:{enumerable:!0,get:()=>!!z(d.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!z(d.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!z(d.validatingFields,n)},error:{enumerable:!0,get:()=>z(d.errors,n)}}),[d,n]),v=Z.useCallback(m=>f.current.onChange({target:{value:gv(m),name:n},type:Qo.CHANGE}),[n]),x=Z.useCallback(()=>f.current.onBlur({target:{value:z(s._formValues,n),name:n},type:Qo.BLUR}),[n,s._formValues]),S=Z.useCallback(m=>{const y=z(s._fields,n);y&&m&&(y._f.ref={focus:()=>m.focus&&m.focus(),select:()=>m.select&&m.select(),setCustomValidity:k=>m.setCustomValidity(k),reportValidity:()=>m.reportValidity()})},[s._fields,n]),p=Z.useMemo(()=>({name:n,value:c,...tt(r)||d.disabled?{disabled:d.disabled||r}:{},onChange:v,onBlur:x,ref:S}),[n,r,d.disabled,v,x,S,c]);return Z.useEffect(()=>{const m=s._options.shouldUnregister||i;s.register(n,{...h.current.rules,...tt(h.current.disabled)?{disabled:h.current.disabled}:{}});const y=(k,j)=>{const C=z(s._fields,k);C&&C._f&&(C._f.mount=j)};if(y(n,!0),m){const k=Ce(z(s._options.defaultValues,n));se(s._defaultValues,n,k),we(z(s._formValues,n))&&se(s._formValues,n,k)}return!a&&s.register(n),()=>{(a?m&&!s._state.action:m)?s.unregister(n):y(n,!1)}},[n,s,a,i]),Z.useEffect(()=>{s._setDisabledField({disabled:r,name:n})},[r,n,s]),Z.useMemo(()=>({field:p,formState:d,fieldState:g}),[p,d,g])}const qi=e=>e.render(Lj(e));var Vj=(e,t,n,r,s)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:s||!0}}:{},Is=e=>Array.isArray(e)?e:[e],Fm=()=>{let e=[];return{get observers(){return e},next:s=>{for(const i of e)i.next&&i.next(s)},subscribe:s=>(e.push(s),{unsubscribe:()=>{e=e.filter(i=>i!==s)}}),unsubscribe:()=>{e=[]}}},qe=e=>be(e)&&!Object.keys(e).length,Od=e=>e.type==="file",_t=e=>typeof e=="function",Xo=e=>{if(!Rd)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Sv=e=>e.type==="select-multiple",zd=e=>e.type==="radio",Fj=e=>zd(e)||bi(e),bl=e=>Xo(e)&&e.isConnected;function Ij(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=we(e)?r++:e[t[r++]];return e}function Oj(e){for(const t in e)if(e.hasOwnProperty(t)&&!we(e[t]))return!1;return!0}function Te(e,t){const n=Array.isArray(t)?t:Na(t)?[t]:Vd(t),r=n.length===1?e:Ij(e,n),s=n.length-1,i=n[s];return r&&delete r[i],s!==0&&(be(r)&&qe(r)||Array.isArray(r)&&Oj(r))&&Te(e,n.slice(0,-1)),e}var bv=e=>{for(const t in e)if(_t(e[t]))return!0;return!1};function Yo(e,t={}){const n=Array.isArray(e);if(be(e)||n)for(const r in e)Array.isArray(e[r])||be(e[r])&&!bv(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Yo(e[r],t[r])):Be(e[r])||(t[r]=!0);return t}function kv(e,t,n){const r=Array.isArray(e);if(be(e)||r)for(const s in e)Array.isArray(e[s])||be(e[s])&&!bv(e[s])?we(t)||Hu(n[s])?n[s]=Array.isArray(e[s])?Yo(e[s],[]):{...Yo(e[s])}:kv(e[s],Be(t)?{}:t[s],n[s]):n[s]=!Gt(e[s],t[s]);return n}var ps=(e,t)=>kv(e,t,Yo(t));const Im={value:!1,isValid:!1},Om={value:!0,isValid:!0};var Tv=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!we(e[0].attributes.value)?we(e[0].value)||e[0].value===""?Om:{value:e[0].value,isValid:!0}:Om:Im}return Im},jv=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>we(e)?e:t?e===""?NaN:e&&+e:n&&Lt(e)?new Date(e):r?r(e):e;const zm={isValid:!1,value:null};var Cv=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,zm):zm;function $m(e){const t=e.ref;return Od(t)?t.files:zd(t)?Cv(e.refs).value:Sv(t)?[...t.selectedOptions].map(({value:n})=>n):bi(t)?Tv(e.refs).value:jv(we(t.value)?e.ref.value:t.value,e)}var zj=(e,t,n,r)=>{const s={};for(const i of e){const o=z(t,i);o&&se(s,i,o._f)}return{criteriaMode:n,names:[...e],fields:s,shouldUseNativeValidation:r}},Zo=e=>e instanceof RegExp,gs=e=>we(e)?e:Zo(e)?e.source:be(e)?Zo(e.value)?e.value.source:e.value:e,Um=e=>({isOnSubmit:!e||e===Nt.onSubmit,isOnBlur:e===Nt.onBlur,isOnChange:e===Nt.onChange,isOnAll:e===Nt.all,isOnTouch:e===Nt.onTouched});const Bm="AsyncFunction";var $j=e=>!!e&&!!e.validate&&!!(_t(e.validate)&&e.validate.constructor.name===Bm||be(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Bm)),Uj=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Hm=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const Os=(e,t,n,r)=>{for(const s of n||Object.keys(e)){const i=z(e,s);if(i){const{_f:o,...a}=i;if(o){if(o.refs&&o.refs[0]&&t(o.refs[0],s)&&!r)return!0;if(o.ref&&t(o.ref,o.name)&&!r)return!0;if(Os(a,t))break}else if(be(a)&&Os(a,t))break}}};function Wm(e,t,n){const r=z(e,n);if(r||Na(n))return{error:r,name:n};const s=n.split(".");for(;s.length;){const i=s.join("."),o=z(t,i),a=z(e,i);if(o&&!Array.isArray(o)&&n!==i)return{name:n};if(a&&a.type)return{name:i,error:a};if(a&&a.root&&a.root.type)return{name:`${i}.root`,error:a.root};s.pop()}return{name:n}}var Bj=(e,t,n,r)=>{n(e);const{name:s,...i}=e;return qe(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(o=>t[o]===(!r||Nt.all))},Hj=(e,t,n)=>!e||!t||e===t||Is(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),Wj=(e,t,n,r,s)=>s.isOnAll?!1:!n&&s.isOnTouch?!(t||e):(n?r.isOnBlur:s.isOnBlur)?!e:(n?r.isOnChange:s.isOnChange)?e:!0,Kj=(e,t)=>!Ld(z(e,t)).length&&Te(e,t),Gj=(e,t,n)=>{const r=Is(z(e,n));return se(r,"root",t[n]),se(e,n,r),e},fo=e=>Lt(e);function Km(e,t,n="validate"){if(fo(e)||Array.isArray(e)&&e.every(fo)||tt(e)&&!e)return{type:n,message:fo(e)?e:"",ref:t}}var gr=e=>be(e)&&!Zo(e)?e:{value:e,message:""},Gm=async(e,t,n,r,s,i)=>{const{ref:o,refs:a,required:u,maxLength:c,minLength:d,min:h,max:f,pattern:g,validate:v,name:x,valueAsNumber:S,mount:p}=e._f,m=z(n,x);if(!p||t.has(x))return{};const y=a?a[0]:o,k=D=>{s&&y.reportValidity&&(y.setCustomValidity(tt(D)?"":D||""),y.reportValidity())},j={},C=zd(o),_=bi(o),T=C||_,E=(S||Od(o))&&we(o.value)&&we(m)||Xo(o)&&o.value===""||m===""||Array.isArray(m)&&!m.length,A=Vj.bind(null,x,r,j),R=(D,F,Y,G=Bt.maxLength,H=Bt.minLength)=>{const U=D?F:Y;j[x]={type:D?G:H,message:U,ref:o,...A(D?G:H,U)}};if(i?!Array.isArray(m)||!m.length:u&&(!T&&(E||Be(m))||tt(m)&&!m||_&&!Tv(a).isValid||C&&!Cv(a).isValid)){const{value:D,message:F}=fo(u)?{value:!!u,message:u}:gr(u);if(D&&(j[x]={type:Bt.required,message:F,ref:y,...A(Bt.required,F)},!r))return k(F),j}if(!E&&(!Be(h)||!Be(f))){let D,F;const Y=gr(f),G=gr(h);if(!Be(m)&&!isNaN(m)){const H=o.valueAsNumber||m&&+m;Be(Y.value)||(D=H>Y.value),Be(G.value)||(F=H<G.value)}else{const H=o.valueAsDate||new Date(m),U=X=>new Date(new Date().toDateString()+" "+X),L=o.type=="time",W=o.type=="week";Lt(Y.value)&&m&&(D=L?U(m)>U(Y.value):W?m>Y.value:H>new Date(Y.value)),Lt(G.value)&&m&&(F=L?U(m)<U(G.value):W?m<G.value:H<new Date(G.value))}if((D||F)&&(R(!!D,Y.message,G.message,Bt.max,Bt.min),!r))return k(j[x].message),j}if((c||d)&&!E&&(Lt(m)||i&&Array.isArray(m))){const D=gr(c),F=gr(d),Y=!Be(D.value)&&m.length>+D.value,G=!Be(F.value)&&m.length<+F.value;if((Y||G)&&(R(Y,D.message,F.message),!r))return k(j[x].message),j}if(g&&!E&&Lt(m)){const{value:D,message:F}=gr(g);if(Zo(D)&&!m.match(D)&&(j[x]={type:Bt.pattern,message:F,ref:o,...A(Bt.pattern,F)},!r))return k(F),j}if(v){if(_t(v)){const D=await v(m,n),F=Km(D,y);if(F&&(j[x]={...F,...A(Bt.validate,F.message)},!r))return k(F.message),j}else if(be(v)){let D={};for(const F in v){if(!qe(D)&&!r)break;const Y=Km(await v[F](m,n),y,F);Y&&(D={...Y,...A(F,Y.message)},k(Y.message),r&&(j[x]=D))}if(!qe(D)&&(j[x]={ref:y,...D},!r))return j}}return k(!0),j};const qj={mode:Nt.onSubmit,reValidateMode:Nt.onChange,shouldFocusError:!0};function Qj(e={}){let t={...qj,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:_t(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},r={},s=be(t.defaultValues)||be(t.values)?Ce(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:Ce(s),o={action:!1,mount:!1,watch:!1},a={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},u,c=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let h={...d};const f={array:Fm(),state:Fm()},g=t.criteriaMode===Nt.all,v=b=>N=>{clearTimeout(c),c=setTimeout(b,N)},x=async b=>{if(!t.disabled&&(d.isValid||h.isValid||b)){const N=t.resolver?qe((await _()).errors):await E(r,!0);N!==n.isValid&&f.state.next({isValid:N})}},S=(b,N)=>{!t.disabled&&(d.isValidating||d.validatingFields||h.isValidating||h.validatingFields)&&((b||Array.from(a.mount)).forEach(M=>{M&&(N?se(n.validatingFields,M,N):Te(n.validatingFields,M))}),f.state.next({validatingFields:n.validatingFields,isValidating:!qe(n.validatingFields)}))},p=(b,N=[],M,$,O=!0,I=!0)=>{if($&&M&&!t.disabled){if(o.action=!0,I&&Array.isArray(z(r,b))){const q=M(z(r,b),$.argA,$.argB);O&&se(r,b,q)}if(I&&Array.isArray(z(n.errors,b))){const q=M(z(n.errors,b),$.argA,$.argB);O&&se(n.errors,b,q),Kj(n.errors,b)}if((d.touchedFields||h.touchedFields)&&I&&Array.isArray(z(n.touchedFields,b))){const q=M(z(n.touchedFields,b),$.argA,$.argB);O&&se(n.touchedFields,b,q)}(d.dirtyFields||h.dirtyFields)&&(n.dirtyFields=ps(s,i)),f.state.next({name:b,isDirty:R(b,N),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else se(i,b,N)},m=(b,N)=>{se(n.errors,b,N),f.state.next({errors:n.errors})},y=b=>{n.errors=b,f.state.next({errors:n.errors,isValid:!1})},k=(b,N,M,$)=>{const O=z(r,b);if(O){const I=z(i,b,we(M)?z(s,b):M);we(I)||$&&$.defaultChecked||N?se(i,b,N?I:$m(O._f)):Y(b,I),o.mount&&x()}},j=(b,N,M,$,O)=>{let I=!1,q=!1;const te={name:b};if(!t.disabled){if(!M||$){(d.isDirty||h.isDirty)&&(q=n.isDirty,n.isDirty=te.isDirty=R(),I=q!==te.isDirty);const re=Gt(z(s,b),N);q=!!z(n.dirtyFields,b),re?Te(n.dirtyFields,b):se(n.dirtyFields,b,!0),te.dirtyFields=n.dirtyFields,I=I||(d.dirtyFields||h.dirtyFields)&&q!==!re}if(M){const re=z(n.touchedFields,b);re||(se(n.touchedFields,b,M),te.touchedFields=n.touchedFields,I=I||(d.touchedFields||h.touchedFields)&&re!==M)}I&&O&&f.state.next(te)}return I?te:{}},C=(b,N,M,$)=>{const O=z(n.errors,b),I=(d.isValid||h.isValid)&&tt(N)&&n.isValid!==N;if(t.delayError&&M?(u=v(()=>m(b,M)),u(t.delayError)):(clearTimeout(c),u=null,M?se(n.errors,b,M):Te(n.errors,b)),(M?!Gt(O,M):O)||!qe($)||I){const q={...$,...I&&tt(N)?{isValid:N}:{},errors:n.errors,name:b};n={...n,...q},f.state.next(q)}},_=async b=>{S(b,!0);const N=await t.resolver(i,t.context,zj(b||a.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return S(b),N},T=async b=>{const{errors:N}=await _(b);if(b)for(const M of b){const $=z(N,M);$?se(n.errors,M,$):Te(n.errors,M)}else n.errors=N;return N},E=async(b,N,M={valid:!0})=>{for(const $ in b){const O=b[$];if(O){const{_f:I,...q}=O;if(I){const te=a.array.has(I.name),re=O._f&&$j(O._f);re&&d.validatingFields&&S([$],!0);const dt=await Gm(O,a.disabled,i,g,t.shouldUseNativeValidation&&!N,te);if(re&&d.validatingFields&&S([$]),dt[I.name]&&(M.valid=!1,N))break;!N&&(z(dt,I.name)?te?Gj(n.errors,dt,I.name):se(n.errors,I.name,dt[I.name]):Te(n.errors,I.name))}!qe(q)&&await E(q,N,M)}}return M.valid},A=()=>{for(const b of a.unMount){const N=z(r,b);N&&(N._f.refs?N._f.refs.every(M=>!bl(M)):!bl(N._f.ref))&&an(b)}a.unMount=new Set},R=(b,N)=>!t.disabled&&(b&&N&&se(i,b,N),!Gt(X(),s)),D=(b,N,M)=>wv(b,a,{...o.mount?i:we(N)?s:Lt(b)?{[b]:N}:N},M,N),F=b=>Ld(z(o.mount?i:s,b,t.shouldUnregister?z(s,b,[]):[])),Y=(b,N,M={})=>{const $=z(r,b);let O=N;if($){const I=$._f;I&&(!I.disabled&&se(i,b,jv(N,I)),O=Xo(I.ref)&&Be(N)?"":N,Sv(I.ref)?[...I.ref.options].forEach(q=>q.selected=O.includes(q.value)):I.refs?bi(I.ref)?I.refs.forEach(q=>{(!q.defaultChecked||!q.disabled)&&(Array.isArray(O)?q.checked=!!O.find(te=>te===q.value):q.checked=O===q.value||!!O)}):I.refs.forEach(q=>q.checked=q.value===O):Od(I.ref)?I.ref.value="":(I.ref.value=O,I.ref.type||f.state.next({name:b,values:Ce(i)})))}(M.shouldDirty||M.shouldTouch)&&j(b,O,M.shouldTouch,M.shouldDirty,!0),M.shouldValidate&&W(b)},G=(b,N,M)=>{for(const $ in N){if(!N.hasOwnProperty($))return;const O=N[$],I=b+"."+$,q=z(r,I);(a.array.has(b)||be(O)||q&&!q._f)&&!Yn(O)?G(I,O,M):Y(I,O,M)}},H=(b,N,M={})=>{const $=z(r,b),O=a.array.has(b),I=Ce(N);se(i,b,I),O?(f.array.next({name:b,values:Ce(i)}),(d.isDirty||d.dirtyFields||h.isDirty||h.dirtyFields)&&M.shouldDirty&&f.state.next({name:b,dirtyFields:ps(s,i),isDirty:R(b,I)})):$&&!$._f&&!Be(I)?G(b,I,M):Y(b,I,M),Hm(b,a)&&f.state.next({...n,name:b}),f.state.next({name:o.mount?b:void 0,values:Ce(i)})},U=async b=>{o.mount=!0;const N=b.target;let M=N.name,$=!0;const O=z(r,M),I=re=>{$=Number.isNaN(re)||Yn(re)&&isNaN(re.getTime())||Gt(re,z(i,M,re))},q=Um(t.mode),te=Um(t.reValidateMode);if(O){let re,dt;const ki=N.type?$m(O._f):gv(b),ln=b.type===Qo.BLUR||b.type===Qo.FOCUS_OUT,Iv=!Uj(O._f)&&!t.resolver&&!z(n.errors,M)&&!O._f.deps||Wj(ln,z(n.touchedFields,M),n.isSubmitted,te,q),Pa=Hm(M,a,ln);se(i,M,ki),ln?(!N||!N.readOnly)&&(O._f.onBlur&&O._f.onBlur(b),u&&u(0)):O._f.onChange&&O._f.onChange(b);const Aa=j(M,ki,ln),Ov=!qe(Aa)||Pa;if(!ln&&f.state.next({name:M,type:b.type,values:Ce(i)}),Iv)return(d.isValid||h.isValid)&&(t.mode==="onBlur"?ln&&x():ln||x()),Ov&&f.state.next({name:M,...Pa?{}:Aa});if(!ln&&Pa&&f.state.next({...n}),t.resolver){const{errors:Gd}=await _([M]);if(I(ki),$){const zv=Wm(n.errors,r,M),qd=Wm(Gd,r,zv.name||M);re=qd.error,M=qd.name,dt=qe(Gd)}}else S([M],!0),re=(await Gm(O,a.disabled,i,g,t.shouldUseNativeValidation))[M],S([M]),I(ki),$&&(re?dt=!1:(d.isValid||h.isValid)&&(dt=await E(r,!0)));$&&(O._f.deps&&W(O._f.deps),C(M,dt,re,Aa))}},L=(b,N)=>{if(z(n.errors,N)&&b.focus)return b.focus(),1},W=async(b,N={})=>{let M,$;const O=Is(b);if(t.resolver){const I=await T(we(b)?b:O);M=qe(I),$=b?!O.some(q=>z(I,q)):M}else b?($=(await Promise.all(O.map(async I=>{const q=z(r,I);return await E(q&&q._f?{[I]:q}:q)}))).every(Boolean),!(!$&&!n.isValid)&&x()):$=M=await E(r);return f.state.next({...!Lt(b)||(d.isValid||h.isValid)&&M!==n.isValid?{}:{name:b},...t.resolver||!b?{isValid:M}:{},errors:n.errors}),N.shouldFocus&&!$&&Os(r,L,b?O:a.mount),$},X=b=>{const N={...o.mount?i:s};return we(b)?N:Lt(b)?z(N,b):b.map(M=>z(N,M))},ce=(b,N)=>({invalid:!!z((N||n).errors,b),isDirty:!!z((N||n).dirtyFields,b),error:z((N||n).errors,b),isValidating:!!z(n.validatingFields,b),isTouched:!!z((N||n).touchedFields,b)}),ke=b=>{b&&Is(b).forEach(N=>Te(n.errors,N)),f.state.next({errors:b?n.errors:{}})},Fn=(b,N,M)=>{const $=(z(r,b,{_f:{}})._f||{}).ref,O=z(n.errors,b)||{},{ref:I,message:q,type:te,...re}=O;se(n.errors,b,{...re,...N,ref:$}),f.state.next({name:b,errors:n.errors,isValid:!1}),M&&M.shouldFocus&&$&&$.focus&&$.focus()},zt=(b,N)=>_t(b)?f.state.subscribe({next:M=>"values"in M&&b(D(void 0,N),M)}):D(b,N,!0),mr=b=>f.state.subscribe({next:N=>{Hj(b.name,N.name,b.exact)&&Bj(N,b.formState||d,Fv,b.reRenderRoot)&&b.callback({values:{...i},...n,...N,defaultValues:s})}}).unsubscribe,$t=b=>(o.mount=!0,h={...h,...b.formState},mr({...b,formState:h})),an=(b,N={})=>{for(const M of b?Is(b):a.mount)a.mount.delete(M),a.array.delete(M),N.keepValue||(Te(r,M),Te(i,M)),!N.keepError&&Te(n.errors,M),!N.keepDirty&&Te(n.dirtyFields,M),!N.keepTouched&&Te(n.touchedFields,M),!N.keepIsValidating&&Te(n.validatingFields,M),!t.shouldUnregister&&!N.keepDefaultValue&&Te(s,M);f.state.next({values:Ce(i)}),f.state.next({...n,...N.keepDirty?{isDirty:R()}:{}}),!N.keepIsValid&&x()},Ud=({disabled:b,name:N})=>{(tt(b)&&o.mount||b||a.disabled.has(N))&&(b?a.disabled.add(N):a.disabled.delete(N))},_a=(b,N={})=>{let M=z(r,b);const $=tt(N.disabled)||tt(t.disabled);return se(r,b,{...M||{},_f:{...M&&M._f?M._f:{ref:{name:b}},name:b,mount:!0,...N}}),a.mount.add(b),M?Ud({disabled:tt(N.disabled)?N.disabled:t.disabled,name:b}):k(b,!0,N.value),{...$?{disabled:N.disabled||t.disabled}:{},...t.progressive?{required:!!N.required,min:gs(N.min),max:gs(N.max),minLength:gs(N.minLength),maxLength:gs(N.maxLength),pattern:gs(N.pattern)}:{},name:b,onChange:U,onBlur:U,ref:O=>{if(O){_a(b,N),M=z(r,b);const I=we(O.value)&&O.querySelectorAll&&O.querySelectorAll("input,select,textarea")[0]||O,q=Fj(I),te=M._f.refs||[];if(q?te.find(re=>re===I):I===M._f.ref)return;se(r,b,{_f:{...M._f,...q?{refs:[...te.filter(bl),I,...Array.isArray(z(s,b))?[{}]:[]],ref:{type:I.type,name:b}}:{ref:I}}}),k(b,!1,void 0,I)}else M=z(r,b,{}),M._f&&(M._f.mount=!1),(t.shouldUnregister||N.shouldUnregister)&&!(yv(a.array,b)&&o.action)&&a.unMount.add(b)}}},Ea=()=>t.shouldFocusError&&Os(r,L,a.mount),Rv=b=>{tt(b)&&(f.state.next({disabled:b}),Os(r,(N,M)=>{const $=z(r,M);$&&(N.disabled=$._f.disabled||b,Array.isArray($._f.refs)&&$._f.refs.forEach(O=>{O.disabled=$._f.disabled||b}))},0,!1))},Bd=(b,N)=>async M=>{let $;M&&(M.preventDefault&&M.preventDefault(),M.persist&&M.persist());let O=Ce(i);if(f.state.next({isSubmitting:!0}),t.resolver){const{errors:I,values:q}=await _();n.errors=I,O=Ce(q)}else await E(r);if(a.disabled.size)for(const I of a.disabled)Te(O,I);if(Te(n.errors,"root"),qe(n.errors)){f.state.next({errors:{}});try{await b(O,M)}catch(I){$=I}}else N&&await N({...n.errors},M),Ea(),setTimeout(Ea);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:qe(n.errors)&&!$,submitCount:n.submitCount+1,errors:n.errors}),$)throw $},Lv=(b,N={})=>{z(r,b)&&(we(N.defaultValue)?H(b,Ce(z(s,b))):(H(b,N.defaultValue),se(s,b,Ce(N.defaultValue))),N.keepTouched||Te(n.touchedFields,b),N.keepDirty||(Te(n.dirtyFields,b),n.isDirty=N.defaultValue?R(b,Ce(z(s,b))):R()),N.keepError||(Te(n.errors,b),d.isValid&&x()),f.state.next({...n}))},Hd=(b,N={})=>{const M=b?Ce(b):s,$=Ce(M),O=qe(b),I=O?s:$;if(N.keepDefaultValues||(s=M),!N.keepValues){if(N.keepDirtyValues){const q=new Set([...a.mount,...Object.keys(ps(s,i))]);for(const te of Array.from(q))z(n.dirtyFields,te)?se(I,te,z(i,te)):H(te,z(I,te))}else{if(Rd&&we(b))for(const q of a.mount){const te=z(r,q);if(te&&te._f){const re=Array.isArray(te._f.refs)?te._f.refs[0]:te._f.ref;if(Xo(re)){const dt=re.closest("form");if(dt){dt.reset();break}}}}if(N.keepFieldsRef)for(const q of a.mount)H(q,z(I,q));else r={}}i=t.shouldUnregister?N.keepDefaultValues?Ce(s):{}:Ce(I),f.array.next({values:{...I}}),f.state.next({values:{...I}})}a={mount:N.keepDirtyValues?a.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!d.isValid||!!N.keepIsValid||!!N.keepDirtyValues,o.watch=!!t.shouldUnregister,f.state.next({submitCount:N.keepSubmitCount?n.submitCount:0,isDirty:O?!1:N.keepDirty?n.isDirty:!!(N.keepDefaultValues&&!Gt(b,s)),isSubmitted:N.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:O?{}:N.keepDirtyValues?N.keepDefaultValues&&i?ps(s,i):n.dirtyFields:N.keepDefaultValues&&b?ps(s,b):N.keepDirty?n.dirtyFields:{},touchedFields:N.keepTouched?n.touchedFields:{},errors:N.keepErrors?n.errors:{},isSubmitSuccessful:N.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:s})},Wd=(b,N)=>Hd(_t(b)?b(i):b,N),Vv=(b,N={})=>{const M=z(r,b),$=M&&M._f;if($){const O=$.refs?$.refs[0]:$.ref;O.focus&&(O.focus(),N.shouldSelect&&_t(O.select)&&O.select())}},Fv=b=>{n={...n,...b}},Kd={control:{register:_a,unregister:an,getFieldState:ce,handleSubmit:Bd,setError:Fn,_subscribe:mr,_runSchema:_,_focusError:Ea,_getWatch:D,_getDirty:R,_setValid:x,_setFieldArray:p,_setDisabledField:Ud,_setErrors:y,_getFieldArray:F,_reset:Hd,_resetDefaultValues:()=>_t(t.defaultValues)&&t.defaultValues().then(b=>{Wd(b,t.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:A,_disableForm:Rv,_subjects:f,_proxyFormState:d,get _fields(){return r},get _formValues(){return i},get _state(){return o},set _state(b){o=b},get _defaultValues(){return s},get _names(){return a},set _names(b){a=b},get _formState(){return n},get _options(){return t},set _options(b){t={...t,...b}}},subscribe:$t,trigger:W,register:_a,handleSubmit:Bd,watch:zt,setValue:H,getValues:X,reset:Wd,resetField:Lv,clearErrors:ke,unregister:an,setError:Fn,setFocus:Vv,getFieldState:ce};return{...Kd,formControl:Kd}}function Nv(e={}){const t=Z.useRef(void 0),n=Z.useRef(void 0),[r,s]=Z.useState({isDirty:!1,isValidating:!1,isLoading:_t(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:_t(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:r},e.defaultValues&&!_t(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:o,...a}=Qj(e);t.current={...a,formState:r}}const i=t.current.control;return i._options=e,Id(()=>{const o=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(a=>({...a,isReady:!0})),i._formState.isReady=!0,o},[i]),Z.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),Z.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),Z.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),Z.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),Z.useEffect(()=>{if(i._proxyFormState.isDirty){const o=i._getDirty();o!==r.isDirty&&i._subjects.state.next({isDirty:o})}},[i,r.isDirty]),Z.useEffect(()=>{e.values&&!Gt(e.values,n.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),n.current=e.values,s(o=>({...o}))):i._resetDefaultValues()},[i,e.values]),Z.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=xv(r,i),t.current}const He={TITLE:{MIN_LENGTH:1,MAX_LENGTH:500},DESCRIPTION:{MAX_LENGTH:5e3},TAGS:{MAX_COUNT:20,MAX_TAG_LENGTH:50,MIN_TAG_LENGTH:1},ESTIMATED_DURATION:{MAX_HOURS:999,MIN_MINUTES:1}},qm=["pending","in_progress","completed","archived","cancelled"],Qm=["very_low","low","medium","high","very_high"];function nr(e){return e?e.trim().replace(/\s+/g," "):""}function Xj(e){const t=[],n=nr(e);return n?(n.length<He.TITLE.MIN_LENGTH&&t.push({field:"title",message:`Title must be at least ${He.TITLE.MIN_LENGTH} character long`,code:"TITLE_TOO_SHORT",value:e}),n.length>He.TITLE.MAX_LENGTH&&t.push({field:"title",message:`Title cannot exceed ${He.TITLE.MAX_LENGTH} characters`,code:"TITLE_TOO_LONG",value:e}),t):(t.push({field:"title",message:"Title is required",code:"TITLE_REQUIRED",value:e}),t)}function Yj(e){const t=[],n=nr(e);return n&&n.length>He.DESCRIPTION.MAX_LENGTH&&t.push({field:"description",message:`Description cannot exceed ${He.DESCRIPTION.MAX_LENGTH} characters`,code:"DESCRIPTION_TOO_LONG",value:e}),t}function Zj(e){const t=[];return e&&!qm.includes(e)&&t.push({field:"status",message:`Invalid status. Must be one of: ${qm.join(", ")}`,code:"INVALID_STATUS",value:e}),t}function Jj(e){const t=[];return e&&!Qm.includes(e)&&t.push({field:"priority",message:`Invalid priority. Must be one of: ${Qm.join(", ")}`,code:"INVALID_PRIORITY",value:e}),t}function eC(e,t=!0){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"due_date",message:"Invalid date format",code:"INVALID_DATE_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"due_date",message:"Invalid date",code:"INVALID_DATE",value:e}),n;!t&&r<new Date&&n.push({field:"due_date",message:"Due date cannot be in the past",code:"DUE_DATE_IN_PAST",value:e});const s=new Date;return s.setFullYear(s.getFullYear()+10),r>s&&n.push({field:"due_date",message:"Due date is too far in the future",code:"DUE_DATE_TOO_FAR",value:e}),n}function tC(e,t){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"reminder_at",message:"Invalid reminder date format",code:"INVALID_REMINDER_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"reminder_at",message:"Invalid reminder date",code:"INVALID_REMINDER_DATE",value:e}),n;if(r<new Date&&n.push({field:"reminder_at",message:"Reminder date cannot be in the past",code:"REMINDER_IN_PAST",value:e}),t){const s=typeof t=="string"?new Date(t):t;!isNaN(s.getTime())&&r>s&&n.push({field:"reminder_at",message:"Reminder date cannot be after due date",code:"REMINDER_AFTER_DUE_DATE",value:e})}return n}function nC(e){const t=[];return e?Array.isArray(e)?(e.length>He.TAGS.MAX_COUNT&&t.push({field:"tags",message:`Cannot have more than ${He.TAGS.MAX_COUNT} tags`,code:"TOO_MANY_TAGS",value:e}),e.forEach((r,s)=>{if(typeof r!="string"){t.push({field:`tags[${s}]`,message:"Tag must be a string",code:"TAG_NOT_STRING",value:r});return}const i=nr(r);i.length<He.TAGS.MIN_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:"Tag cannot be empty",code:"TAG_EMPTY",value:r}),i.length>He.TAGS.MAX_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:`Tag cannot exceed ${He.TAGS.MAX_TAG_LENGTH} characters`,code:"TAG_TOO_LONG",value:r})}),new Set(e.map(r=>nr(r).toLowerCase())).size!==e.length&&t.push({field:"tags",message:"Duplicate tags are not allowed",code:"DUPLICATE_TAGS",value:e}),t):(t.push({field:"tags",message:"Tags must be an array",code:"TAGS_NOT_ARRAY",value:e}),t):t}function rC(e){const t=[];if(!e)return t;const n=/^PT(?:(\d+)H)?(?:(\d+)M)?$/,r=e.match(n);if(!r)return t.push({field:"estimated_duration",message:"Invalid duration format. Use format like PT1H30M (1 hour 30 minutes)",code:"INVALID_DURATION_FORMAT",value:e}),t;const s=parseInt(r[1]||"0",10),i=parseInt(r[2]||"0",10);return s>He.ESTIMATED_DURATION.MAX_HOURS&&t.push({field:"estimated_duration",message:`Duration cannot exceed ${He.ESTIMATED_DURATION.MAX_HOURS} hours`,code:"DURATION_TOO_LONG",value:e}),s===0&&i<He.ESTIMATED_DURATION.MIN_MINUTES&&t.push({field:"estimated_duration",message:`Duration must be at least ${He.ESTIMATED_DURATION.MIN_MINUTES} minute`,code:"DURATION_TOO_SHORT",value:e}),t}function sC(e){const t=[];return e?(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"user_id",message:"Invalid user ID format",code:"INVALID_USER_ID_FORMAT",value:e}),t):(t.push({field:"user_id",message:"User ID is required",code:"USER_ID_REQUIRED",value:e}),t)}function iC(e){const t=[];return e&&(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"category_id",message:"Invalid category ID format",code:"INVALID_CATEGORY_ID_FORMAT",value:e})),t}function kl(e){const t={...e};return t.title!==void 0&&(t.title=nr(t.title)),t.description!==void 0&&(t.description=nr(t.description)||void 0),t.tags&&(t.tags=t.tags.map(n=>nr(n)).filter(n=>n.length>0).filter((n,r,s)=>s.indexOf(n)===r)),t.metadata===void 0?t.metadata={}:(typeof t.metadata!="object"||t.metadata===null)&&(t.metadata={}),t}function Xm(e,t={}){const{isCreate:n=!1,allowPastDueDates:r=!0,requireUserId:s=!0}=t,i=[];return(n||e.title!==void 0)&&i.push(...Xj(e.title)),s&&(n||e.user_id!==void 0)&&i.push(...sC(e.user_id)),e.description!==void 0&&i.push(...Yj(e.description)),e.status!==void 0&&i.push(...Zj(e.status)),e.priority!==void 0&&i.push(...Jj(e.priority)),e.due_date!==void 0&&i.push(...eC(e.due_date,r)),e.reminder_at!==void 0&&i.push(...tC(e.reminder_at,e.due_date)),e.tags!==void 0&&i.push(...nC(e.tags)),e.estimated_duration!==void 0&&i.push(...rC(e.estimated_duration)),e.category_id!==void 0&&i.push(...iC(e.category_id)),{isValid:i.length===0,errors:i}}function oC(e,t){return e.filter(n=>n.field===t||n.field.startsWith(`${t}[`))}function aC(e={}){const[t,n]=w.useState({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1}),r=w.useCallback(h=>{const f={};h.errors.forEach(g=>{const v=g.field;f[v]||(f[v]=[]),f[v].push(g)}),n({isValid:h.isValid,errors:h.errors,fieldErrors:f,hasErrors:h.errors.length>0,isValidating:!1})},[]),s=w.useCallback(h=>{n(v=>({...v,isValidating:!0}));const f=kl(h),g=Xm(f,e);return r(g),g},[e,r]),i=w.useCallback((h,f,g={})=>{const v={...g,[h]:f},x=kl(v),S=Xm(x,{...e,isCreate:!1});return oC(S.errors,h)},[e]),o=w.useCallback(()=>{n({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1})},[]),a=w.useCallback(h=>{n(f=>{const g={...f.fieldErrors};delete g[h];const v=f.errors.filter(x=>!x.field.startsWith(h)&&x.field!==h);return{...f,errors:v,fieldErrors:g,hasErrors:v.length>0,isValid:v.length===0}})},[]),u=w.useCallback(h=>{const f=t.fieldErrors[h];return!f||f.length===0?null:f.length===1?f[0].message:f.map(g=>g.message).join(", ")},[t.fieldErrors]),c=w.useCallback(h=>{var f;return((f=t.fieldErrors[h])==null?void 0:f.length)>0},[t.fieldErrors]),d=w.useCallback(h=>kl(h),[]);return{validationState:t,validateData:s,validateField:i,clearValidation:o,clearFieldErrors:a,getFieldErrorMessage:u,hasFieldError:c,sanitizeData:d}}const lC=({toast:e,onDismiss:t})=>{const[n,r]=w.useState(!0);w.useEffect(()=>{if(e.duration&&e.duration>0){const u=setTimeout(()=>{r(!1),setTimeout(()=>t(e.id),300)},e.duration);return()=>clearTimeout(u)}},[e.duration,e.id,t]);const s=()=>{r(!1),setTimeout(()=>t(e.id),300)},i=()=>{switch(e.type){case"success":return l.jsx(Hn,{className:"w-5 h-5 text-green-500"});case"error":return l.jsx(xd,{className:"w-5 h-5 text-red-500"});case"warning":return l.jsx(jd,{className:"w-5 h-5 text-yellow-500"});case"info":return l.jsx(bm,{className:"w-5 h-5 text-blue-500"});default:return l.jsx(bm,{className:"w-5 h-5 text-blue-500"})}},o=()=>{switch(e.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-blue-50 border-blue-200"}},a=()=>{switch(e.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-blue-800"}};return l.jsx(ae,{children:n&&l.jsx(P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3,ease:"easeOut"},className:`
            relative max-w-sm w-full border rounded-lg shadow-lg backdrop-blur-sm
            ${o()}
          `,children:l.jsx("div",{className:"p-4",children:l.jsxs("div",{className:"flex items-start space-x-3",children:[l.jsx("div",{className:"flex-shrink-0",children:i()}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h4",{className:`text-sm font-medium ${a()}`,children:e.title}),e.message&&l.jsx("p",{className:`mt-1 text-sm ${a()} opacity-80`,children:e.message}),e.action&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:e.action.onClick,className:`
                        text-sm font-medium underline hover:no-underline
                        ${a()}
                      `,children:e.action.label})})]}),l.jsx("button",{onClick:s,className:`
                  flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10
                  ${a()} opacity-60 hover:opacity-100
                `,children:l.jsx(wt,{className:"w-4 h-4"})})]})})})})},uC=({toasts:e,onDismiss:t,position:n="top-right"})=>{const r=()=>{switch(n){case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-center":return"top-4 left-1/2 transform -translate-x-1/2";case"bottom-center":return"bottom-4 left-1/2 transform -translate-x-1/2";default:return"top-4 right-4"}};return l.jsx("div",{className:`fixed z-50 ${r()}`,children:l.jsx("div",{className:"space-y-3",children:l.jsx(ae,{children:e.map(s=>l.jsx(lC,{toast:s,onDismiss:t},s.id))})})})};function _v(){const[e,t]=w.useState([]),n=w.useCallback(c=>{const d=Math.random().toString(36).substr(2,9),h={...c,id:d,duration:c.duration??5e3};return t(f=>[...f,h]),d},[]),r=w.useCallback(c=>{t(d=>d.filter(h=>h.id!==c))},[]),s=w.useCallback(()=>{t([])},[]),i=w.useCallback((c,d,h)=>n({...h,type:"success",title:c,message:d}),[n]),o=w.useCallback((c,d,h)=>n({...h,type:"error",title:c,message:d,duration:(h==null?void 0:h.duration)??7e3}),[n]),a=w.useCallback((c,d,h)=>n({...h,type:"warning",title:c,message:d}),[n]),u=w.useCallback((c,d,h)=>n({...h,type:"info",title:c,message:d}),[n]);return{toasts:e,addToast:n,dismissToast:r,dismissAll:s,success:i,error:o,warning:a,info:u}}const cC={folder:Jt,tag:hr,star:Td,heart:wd,zap:Cd,home:Sd,briefcase:Gy,"shopping-cart":ev,calendar:Ln,book:Ky,music:Zy,camera:qy,coffee:Qy},dC=({categories:e,selectedCategoryId:t,onSelect:n,onCreateNew:r,placeholder:s="Select a category...",disabled:i=!1,className:o=""})=>{const[a,u]=w.useState(!1),[c,d]=w.useState(""),h=w.useRef(null),f=w.useRef(null),g=e.find(y=>y.id===t),v=e.filter(y=>y.name.toLowerCase().includes(c.toLowerCase()));w.useEffect(()=>{const y=k=>{h.current&&!h.current.contains(k.target)&&(u(!1),d(""))};return document.addEventListener("mousedown",y),()=>document.removeEventListener("mousedown",y)},[]),w.useEffect(()=>{a&&f.current&&f.current.focus()},[a]);const x=()=>{i||(u(!a),d(""))},S=y=>{n(y),u(!1),d("")},p=()=>{r&&(r(),u(!1),d(""))},m=y=>(y?cC[y]:Jt)||Jt;return l.jsxs("div",{className:`relative ${o}`,ref:h,children:[l.jsxs(P.button,{type:"button",whileHover:i?{}:{scale:1.01},whileTap:i?{}:{scale:.99},onClick:x,disabled:i,className:`w-full flex items-center justify-between p-3 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 text-fa-gray-400 cursor-not-allowed":a?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-300 bg-white hover:border-fa-gray-400"}`,children:[l.jsx("div",{className:"flex items-center space-x-3",children:g?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:g.color},children:Z.createElement(m(g.icon),{className:"w-3 h-3 text-white"})}),l.jsx("span",{className:"text-fa-gray-800",children:g.name})]}):l.jsxs(l.Fragment,{children:[l.jsx(Jt,{className:"w-5 h-5 text-fa-gray-400"}),l.jsx("span",{className:"text-fa-gray-500",children:s})]})}),l.jsxs("div",{className:"flex items-center space-x-2",children:[g&&l.jsx(P.button,{type:"button",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y=>{y.stopPropagation(),S(void 0)},className:"p-1 rounded text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(wt,{className:"w-3 h-3"})}),l.jsx(Kt,{className:`w-4 h-4 text-fa-gray-400 transition-transform duration-200 ${a?"rotate-180":""}`})]})]}),l.jsx(ae,{children:a&&l.jsxs(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden",children:[l.jsx("div",{className:"p-3 border-b border-fa-gray-100",children:l.jsxs("div",{className:"relative",children:[l.jsx(Vu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400"}),l.jsx("input",{ref:f,type:"text",value:c,onChange:y=>d(y.target.value),placeholder:"Search categories...",className:"w-full pl-10 pr-4 py-2 border border-fa-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-fa-blue-400 focus:border-transparent"})]})}),l.jsxs("div",{className:"max-h-48 overflow-y-auto",children:[l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(void 0),className:"w-full flex items-center space-x-3 p-3 text-left hover:bg-fa-gray-50 transition-colors duration-150",children:[l.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-gray-300 flex items-center justify-center",children:l.jsx(wt,{className:"w-3 h-3 text-fa-gray-400"})}),l.jsx("span",{className:"text-fa-gray-500 italic",children:"No category"})]}),v.map(y=>{const k=m(y.icon);return l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(y.id),className:`w-full flex items-center space-x-3 p-3 text-left transition-colors duration-150 ${t===y.id?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[l.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:y.color},children:l.jsx(k,{className:"w-3 h-3 text-white"})}),l.jsx("span",{className:"flex-1",children:y.name}),y.is_default&&l.jsx(Td,{className:"w-3 h-3 text-fa-yellow-500"})]},y.id)}),c&&v.length===0&&l.jsx("div",{className:"p-3 text-center text-fa-gray-500 text-sm",children:"No categories found"}),r&&l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(59, 130, 246, 0.05)"},onClick:p,className:"w-full flex items-center space-x-3 p-3 text-left border-t border-fa-gray-100 text-fa-blue-600 hover:bg-fa-blue-50 transition-colors duration-150",children:[l.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-blue-300 flex items-center justify-center",children:l.jsx(Sa,{className:"w-3 h-3"})}),l.jsx("span",{children:"Create new category"})]})]})]})})]})},fC=({tags:e,onChange:t,suggestions:n=[],placeholder:r="Add a tag...",maxTags:s=10,disabled:i=!1,className:o="",enableSmartSuggestions:a=!0})=>{const[u,c]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(-1),[v,x]=w.useState([]),[S,p]=w.useState(!1),m=w.useRef(null),y=w.useRef(null),k=w.useRef(null),j=w.useCallback(async G=>{if(a){p(!0);try{const H=await Ts.getAutocompleteSuggestions(G,e,5);x(H)}catch(H){console.error("Error fetching smart suggestions:",H),x([])}finally{p(!1)}}},[e,a]),_=(a?v:n).filter(G=>G.toLowerCase().includes(u.toLowerCase())&&!e.includes(G)).slice(0,5),T=G=>{const H=G.target.value;c(H),g(-1),k.current&&clearTimeout(k.current),a&&(k.current=setTimeout(()=>{j(H)},300));const U=!a&&n.length>0,L=a&&v.length>0;h(H.length>0&&(U||L))},E=G=>{if(!i)switch(G.key){case"Enter":case",":G.preventDefault(),f>=0&&_[f]?A(_[f]):u.trim()&&A(u.trim());break;case"ArrowDown":G.preventDefault(),d&&g(H=>H<_.length-1?H+1:H);break;case"ArrowUp":G.preventDefault(),d&&g(H=>H>0?H-1:-1);break;case"Escape":h(!1),g(-1);break;case"Backspace":u===""&&e.length>0&&R(e[e.length-1]);break}},A=G=>{const H=G.trim().toLowerCase();H&&!e.includes(H)&&e.length<s&&t([...e,H]),c(""),h(!1),g(-1)},R=G=>{t(e.filter(H=>H!==G))},D=G=>{A(G)},F=()=>{setTimeout(()=>{h(!1),g(-1)},150)},Y=()=>{i||(a&&u.length===0&&j(""),(u.length>0&&_.length>0||a)&&h(!0))};return w.useEffect(()=>()=>{k.current&&clearTimeout(k.current)},[]),w.useEffect(()=>{a&&j("")},[j,a]),l.jsxs("div",{className:`relative ${o}`,children:[l.jsx("div",{className:`min-h-[42px] p-2 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 cursor-not-allowed":"bg-white border-fa-gray-300 hover:border-fa-gray-400 focus-within:border-fa-blue-400 focus-within:ring-2 focus-within:ring-fa-blue-400 focus-within:ring-opacity-20"}`,children:l.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[l.jsx(ae,{children:e.map((G,H)=>l.jsxs(P.span,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"inline-flex items-center px-2 py-1 rounded-full text-sm bg-fa-blue-100 text-fa-blue-800",children:[l.jsx(Sm,{className:"w-3 h-3 mr-1"}),G,!i&&l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},type:"button",onClick:()=>R(G),className:"ml-1 text-fa-blue-600 hover:text-fa-blue-800",children:l.jsx(wt,{className:"w-3 h-3"})})]},G))}),e.length<s&&l.jsx("input",{ref:m,type:"text",value:u,onChange:T,onKeyDown:E,onBlur:F,onFocus:Y,disabled:i,placeholder:e.length===0?r:"",className:"flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder-fa-gray-400 disabled:cursor-not-allowed"}),u.trim()&&!i&&l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:()=>A(u.trim()),className:"p-1 rounded text-fa-blue-600 hover:bg-fa-blue-100",children:l.jsx(Sa,{className:"w-4 h-4"})})]})}),l.jsx(ae,{children:d&&(_.length>0||S)&&l.jsx(P.div,{ref:y,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-1 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-40 overflow-y-auto",children:S?l.jsxs("div",{className:"flex items-center justify-center px-3 py-2",children:[l.jsx(Yk,{className:"w-4 h-4 animate-spin text-fa-gray-400 mr-2"}),l.jsx("span",{className:"text-sm text-fa-gray-500",children:"Loading suggestions..."})]}):_.map((G,H)=>l.jsxs(P.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>D(G),className:`w-full flex items-center space-x-2 px-3 py-2 text-left transition-colors duration-150 ${H===f?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"} ${H===0?"rounded-t-lg":""} ${H===_.length-1?"rounded-b-lg":""}`,children:[l.jsx(Sm,{className:"w-4 h-4 text-fa-gray-400"}),l.jsx("span",{children:G})]},G))})}),e.length>=s&&l.jsxs("p",{className:"text-xs text-fa-gray-500 mt-1",children:["Maximum ",s," tags allowed"]})]})},hC=["#007bff","#28a745","#dc3545","#ffc107","#6f42c1","#fd7e14","#20c997","#e83e8c","#6c757d","#17a2b8","#343a40","#f8f9fa","#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#ffeaa7","#dda0dd","#98d8c8","#f7dc6f","#bb8fce","#85c1e9","#f8c471","#82e0aa"],mC=()=>{const e=Math.floor(Math.random()*360),t=Math.floor(Math.random()*40)+60,n=Math.floor(Math.random()*30)+40;return((s,i,o)=>{o/=100;const a=i*Math.min(o,1-o)/100,u=c=>{const d=(c+s/30)%12,h=o-a*Math.max(Math.min(d-3,9-d,1),-1);return Math.round(255*h).toString(16).padStart(2,"0")};return`#${u(0)}${u(8)}${u(4)}`})(e,t,n)},pC=({selectedColor:e,onColorSelect:t,className:n=""})=>{const[r,s]=w.useState(e),[i,o]=w.useState(!1),a=h=>{t(h),s(h)},u=h=>{const f=h.target.value;s(f),t(f)},c=()=>{const h=mC();t(h),s(h)},d=h=>h===e;return l.jsxs("div",{className:`space-y-4 ${n}`,children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Choose a color"}),l.jsx("div",{className:"grid grid-cols-8 gap-2",children:hC.map(h=>l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>a(h),className:`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${d(h)?"border-fa-gray-800 shadow-lg":"border-fa-gray-200 hover:border-fa-gray-400"}`,style:{backgroundColor:h},title:h,children:l.jsx(ae,{children:d(h)&&l.jsx(P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},className:"w-full h-full flex items-center justify-center",children:l.jsx(Ho,{className:"w-4 h-4 text-white drop-shadow-sm"})})})},h))})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:c,className:"flex items-center space-x-2 px-3 py-2 bg-fa-blue-100 text-fa-blue-700 rounded-lg hover:bg-fa-blue-200 transition-colors",children:[l.jsx(Jy,{className:"w-4 h-4"}),l.jsx("span",{children:"Random"})]}),l.jsxs(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>o(!i),className:"flex items-center space-x-2 px-3 py-2 bg-fa-gray-100 text-fa-gray-700 rounded-lg hover:bg-fa-gray-200 transition-colors",children:[l.jsx(eT,{className:"w-4 h-4"}),l.jsx("span",{children:"Custom"})]})]}),l.jsx(ae,{children:i&&l.jsxs(P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700",children:"Custom color"}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{type:"color",value:r,onChange:u,className:"w-12 h-10 border border-fa-gray-300 rounded-lg cursor-pointer"}),l.jsx("input",{type:"text",value:r,onChange:h=>{const f=h.target.value;s(f),/^#[0-9A-F]{6}$/i.test(f)&&t(f)},placeholder:"#007bff",className:"flex-1 px-3 py-2 border border-fa-gray-300 rounded-lg focus:ring-2 focus:ring-fa-blue-500 focus:border-transparent"})]}),l.jsx("p",{className:"text-xs text-fa-gray-500",children:"Enter a hex color code (e.g., #007bff) or use the color picker"})]})}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-fa-gray-50 rounded-lg",children:[l.jsx("div",{className:"w-6 h-6 rounded border border-fa-gray-300",style:{backgroundColor:e}}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-fa-gray-800",children:"Selected Color"}),l.jsx("p",{className:"text-xs text-fa-gray-500",children:e})]})]})]})},Ym=[{icon:Jt,name:"folder"},{icon:hr,name:"tag"},{icon:Td,name:"star"},{icon:wd,name:"heart"},{icon:Cd,name:"zap"},{icon:Sd,name:"home"},{icon:Gy,name:"briefcase"},{icon:ev,name:"shopping-cart"},{icon:Ln,name:"calendar"},{icon:Ky,name:"book"},{icon:Zy,name:"music"},{icon:qy,name:"camera"},{icon:Qy,name:"coffee"}],gC=({isOpen:e,onClose:t,onSubmit:n,category:r,mode:s})=>{const[i,o]=w.useState(!1),[a,u]=w.useState(null),{register:c,handleSubmit:d,control:h,watch:f,setValue:g,reset:v,formState:{errors:x,isValid:S}}=Nv({defaultValues:{name:"",color:"#007bff",icon:"folder",is_default:!1},mode:"onChange"}),p=f("color"),m=f("icon");w.useEffect(()=>{e&&(v(s==="edit"&&r?{name:r.name,color:r.color,icon:r.icon||"folder",is_default:r.is_default}:{name:"",color:"#007bff",icon:"folder",is_default:!1}),u(null))},[e,s,r,v]);const y=async C=>{o(!0),u(null);try{await n({name:C.name.trim(),color:C.color,icon:C.icon,is_default:C.is_default}),t()}catch(_){const T=_ instanceof Error?_.message:"An error occurred";u(T)}finally{o(!1)}},k=()=>{const C=Ym.find(_=>_.name===m);return C?C.icon:Jt};if(!e)return null;const j=k();return l.jsx(ae,{children:l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"fa-glass-panel w-full max-w-md max-h-[90vh] overflow-y-auto",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-white-glass",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{backgroundColor:p},children:l.jsx(j,{className:"w-4 h-4 text-white"})}),l.jsx("h2",{className:"fa-h3 text-fa-gray-800",children:s==="create"?"Create Category":"Edit Category"})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:t,className:"p-2 rounded-lg text-fa-gray-500 hover:bg-fa-white-glass",children:l.jsx(wt,{className:"w-5 h-5"})})]}),l.jsxs("form",{onSubmit:d(y),className:"p-6 space-y-6",children:[a&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"p-4 bg-fa-error bg-opacity-10 border border-fa-error rounded-lg",children:l.jsx("p",{className:"text-fa-error text-sm",children:a})}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Category Name"}),l.jsx("input",{...c("name",{required:"Category name is required",minLength:{value:1,message:"Name must be at least 1 character"},maxLength:{value:50,message:"Name must be less than 50 characters"}}),className:"fa-input w-full",placeholder:"Enter category name...",autoFocus:!0}),x.name&&l.jsx("p",{className:"text-fa-error text-sm mt-1",children:x.name.message})]}),l.jsx(pC,{selectedColor:p,onColorSelect:C=>g("color",C)}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Icon"}),l.jsx("div",{className:"grid grid-cols-6 gap-2",children:Ym.map(({icon:C,name:_})=>l.jsx(P.button,{type:"button",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>g("icon",_),className:`p-3 rounded-lg border transition-all duration-200 flex items-center justify-center ${m===_?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-200 hover:border-fa-gray-400 hover:bg-fa-gray-50"}`,children:l.jsx(C,{className:"w-5 h-5 text-fa-gray-600"})},_))})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{...c("is_default"),type:"checkbox",id:"is_default",className:"w-4 h-4 text-fa-blue-600 bg-fa-gray-100 border-fa-gray-300 rounded focus:ring-fa-blue-500"}),l.jsx("label",{htmlFor:"is_default",className:"text-sm text-fa-gray-700",children:"Set as default category"})]}),l.jsxs("div",{className:"flex space-x-3 pt-4",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"flex-1 fa-button-secondary",children:"Cancel"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:!S||i,className:"flex-1 fa-button-primary disabled:opacity-50 disabled:cursor-not-allowed",children:i?l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),l.jsx("span",{children:"Saving..."})]}):l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(kd,{className:"w-4 h-4"}),l.jsx("span",{children:s==="create"?"Create":"Save"})]})})]})]})]})})})},yC=jj(),vC=[{value:"pending",label:"Pending",color:"text-fa-gray-500"},{value:"in_progress",label:"In Progress",color:"text-fa-blue-500"},{value:"completed",label:"Completed",color:"text-fa-green-500"},{value:"archived",label:"Archived",color:"text-fa-gray-400"},{value:"cancelled",label:"Cancelled",color:"text-fa-red-500"}],Ev=({isOpen:e,onClose:t,onSubmit:n,todo:r,mode:s})=>{var A,R;const[i,o]=w.useState(!1),[a,u]=w.useState(null),[c,d]=w.useState(!1),h=aC({isCreate:s==="create",allowPastDueDates:!0,requireUserId:!1}),f=_v(),{categories:g,getAllTags:v,createCategory:x,loadCategories:S}=Md(),{register:p,handleSubmit:m,control:y,reset:k,formState:{errors:j,isValid:C}}=Nv({defaultValues:{title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0},mode:"onChange"});w.useEffect(()=>{if(e){if(s==="edit"&&r){const D=r.due_date?new Date(r.due_date).toISOString().split("T")[0]:"",F=r.reminder_at?new Date(r.reminder_at).toISOString().slice(0,16):"";let Y=0,G=0;if(r.estimated_duration){const H=r.estimated_duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);H&&(Y=parseInt(H[1]||"0",10),G=parseInt(H[2]||"0",10))}k({title:r.title,description:r.description||"",priority:r.priority,status:r.status,category_id:r.category_id,due_date:D,reminder_at:F,tags:r.tags,estimated_duration_hours:Y,estimated_duration_minutes:G})}else k({title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0});u(null)}},[e,s,r,k]);const _=(D,F)=>D===0&&F===0?"":`PT${D>0?`${D}H`:""}${F>0?`${F}M`:""}`,T=async D=>{o(!0),u(null);try{const F=_(D.estimated_duration_hours,D.estimated_duration_minutes),Y={title:D.title.trim(),description:D.description.trim()||void 0,priority:D.priority,status:D.status,category_id:D.category_id||void 0,due_date:D.due_date?new Date(D.due_date):void 0,tags:D.tags,estimated_duration:F||void 0},G=h.validateData(Y);if(!G.isValid){const H=G.errors.length===1?G.errors[0].message:`Please fix ${G.errors.length} validation errors`;u(H),f.error("Validation Error",H);return}if(s==="create"){const H=Y,U=await Oe.createTodo(H);f.success("Todo Created","Your todo has been created successfully"),n(U)}else{if(!r)throw new Error("Todo is required for edit mode");const H=Y,U=await Oe.updateTodo(r.id,H);f.success("Todo Updated","Your todo has been updated successfully"),n(U)}t()}catch(F){const Y=F instanceof Error?F.message:"An error occurred";u(Y),Y.includes("validation")?f.error("Validation Error",Y):Y.includes("not found")?f.error("Todo Not Found","The todo you are trying to edit no longer exists"):f.error("Operation Failed",Y)}finally{o(!1)}},E=async D=>{try{await x(D),await S(),f.success("Category Created",`"${D.name}" has been created successfully`)}catch(F){const Y=F instanceof Error?F.message:"Failed to create category";throw f.error("Create Category Failed",Y),F}};return e?l.jsxs(l.Fragment,{children:[l.jsx(ae,{children:e&&l.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center z-50 p-4",onClick:t,children:l.jsxs(P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"fa-glass-panel-frosted w-full max-w-2xl max-h-[90vh] overflow-y-auto",onClick:D=>D.stopPropagation(),children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-gray-200",children:[l.jsx("h2",{className:"fa-heading-2",children:s==="create"?"Create New Todo":"Edit Todo"}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-500 hover:text-fa-gray-700 rounded-lg hover:bg-fa-gray-100",children:l.jsx(wt,{className:"w-5 h-5"})})]}),a&&l.jsxs(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm flex items-center space-x-2",children:[l.jsx(xd,{className:"w-4 h-4"}),l.jsx("span",{children:a})]}),l.jsxs("form",{onSubmit:m(T),className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Title *"}),l.jsx("input",{id:"title",type:"text",...p("title",{required:"Title is required",minLength:{value:1,message:"Title cannot be empty"},maxLength:{value:200,message:"Title is too long"}}),className:`fa-input ${j.title?"border-red-500":""}`,placeholder:"What needs to be done?",autoFocus:!0}),j.title&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:j.title.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Description"}),l.jsx("textarea",{id:"description",...p("description"),className:"fa-input min-h-[100px] resize-y",placeholder:"Add more details...",rows:4})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"priority",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Priority"}),l.jsx(qi,{name:"priority",control:y,render:({field:D})=>l.jsxs("div",{className:"space-y-2",children:[l.jsx("select",{...D,className:"fa-input",children:yC.map(F=>l.jsx("option",{value:F.value,children:F.label},F.value))}),l.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-fa-gray-50 rounded-lg",children:[l.jsx("span",{className:"text-sm text-fa-gray-600",children:"Preview:"}),l.jsx(qo,{priority:D.value,variant:"chip",size:"sm",showLabel:!0,animated:!0})]})]})})]}),s==="edit"&&l.jsxs("div",{children:[l.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Status"}),l.jsx(qi,{name:"status",control:y,render:({field:D})=>l.jsx("select",{...D,className:"fa-input",children:vC.map(F=>l.jsx("option",{value:F.value,children:F.label},F.value))})})]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Jt,{className:"w-4 h-4 inline mr-1"}),"Category"]}),l.jsx(qi,{name:"category_id",control:y,render:({field:D})=>l.jsx(dC,{categories:g,selectedCategoryId:D.value,onSelect:D.onChange,onCreateNew:()=>d(!0),placeholder:"Select a category..."})})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsxs("label",{htmlFor:"due_date",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(Ln,{className:"w-4 h-4 inline mr-1"}),"Due Date"]}),l.jsx("input",{id:"due_date",type:"date",...p("due_date"),className:"fa-input"})]}),l.jsxs("div",{children:[l.jsxs("label",{htmlFor:"reminder_at",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(wm,{className:"w-4 h-4 inline mr-1"}),"Reminder"]}),l.jsx("input",{id:"reminder_at",type:"datetime-local",...p("reminder_at"),className:"fa-input"})]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(hr,{className:"w-4 h-4 inline mr-1"}),"Tags"]}),l.jsx(qi,{name:"tags",control:y,render:({field:D})=>l.jsx(fC,{tags:D.value,onChange:D.onChange,suggestions:v(),placeholder:"Add tags...",maxTags:10,enableSmartSuggestions:!0})})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[l.jsx(wm,{className:"w-4 h-4 inline mr-1"}),"Estimated Duration"]}),l.jsxs("div",{className:"flex space-x-4 items-center",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"number",...p("estimated_duration_hours",{min:{value:0,message:"Hours must be positive"},max:{value:999,message:"Hours must be less than 1000"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"999"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"hours"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"number",...p("estimated_duration_minutes",{min:{value:0,message:"Minutes must be positive"},max:{value:59,message:"Minutes must be less than 60"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"59"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"minutes"})]})]}),(j.estimated_duration_hours||j.estimated_duration_minutes)&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:((A=j.estimated_duration_hours)==null?void 0:A.message)||((R=j.estimated_duration_minutes)==null?void 0:R.message)})]}),l.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-fa-gray-200",children:[l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"px-6 py-3 fa-button-glass disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:i||!C,className:"px-6 py-3 fa-button-primary text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:i?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),l.jsx("span",{children:s==="create"?"Creating...":"Saving..."})]}):l.jsxs(l.Fragment,{children:[l.jsx(kd,{className:"w-4 h-4"}),l.jsx("span",{children:s==="create"?"Create Todo":"Save Changes"})]})})]})]})]})},"todo-form-modal")}),l.jsx(gC,{isOpen:c,onClose:()=>d(!1),onSubmit:E,mode:"create"})]}):null},Pv=({todo:e,onUpdate:t,onDelete:n})=>{var A;const[r,s]=w.useState(!1),[i,o]=w.useState(!1),[a,u]=w.useState(e.title),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!1),{categories:x}=Md(),S=x.find(R=>R.id===e.category_id),p=e.status==="completed",m=Oe.isOverdue(e),y=w.useCallback(async()=>{if(!c){d(!0);try{const R=await Oe.toggleTodoCompletion(e.id,e.status);t==null||t(R)}catch(R){console.error("Failed to toggle todo completion:",R)}finally{d(!1)}}},[e.id,e.status,c,t]),k=w.useCallback(async()=>{if(!c){d(!0);try{await Oe.deleteTodo(e.id),n==null||n(e.id),f(!1)}catch(R){console.error("Failed to delete todo:",R)}finally{d(!1)}}},[e.id,c,n]),j=()=>{v(!0)},C=R=>{t==null||t(R),v(!1)},_=w.useCallback(async()=>{if(c||a.trim()===e.title){o(!1);return}d(!0);try{const R=await Oe.updateTodo(e.id,{title:a.trim()});t==null||t(R),o(!1)}catch(R){console.error("Failed to update todo:",R),u(e.title)}finally{d(!1)}},[e.id,e.title,a,c,t]),T=()=>{u(e.title),o(!1)},E=()=>{switch(e.status){case"completed":return"text-fa-success";case"in_progress":return"text-fa-info";case"pending":return"text-fa-gray-500";case"cancelled":return"text-fa-error";case"archived":return"text-fa-gray-400";default:return"text-fa-gray-500"}};return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:`fa-todo-card relative transition-all duration-300 ${p?"opacity-70":""} ${m?"border-l-4 border-fa-error":""} ${co(e.priority)?"ring-2 ring-red-500/30 shadow-lg shadow-red-500/20":""} ${Rm(e.priority)&&!co(e.priority)?"ring-1 ring-orange-500/20":""}`,onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),children:[l.jsxs("div",{className:"flex items-start",children:[l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y,disabled:c,className:`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 transition-all duration-200 ${p?"bg-fa-success border-fa-success text-white":"border-fa-gray-300 hover:border-fa-blue-400"} ${c?"opacity-50 cursor-not-allowed":""}`,children:c?l.jsx("div",{className:"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"}):p&&l.jsx(Ho,{className:"w-4 h-4"})}),l.jsx("div",{className:"flex-1 min-w-0",children:i?l.jsxs("div",{className:"space-y-2",children:[l.jsx("input",{type:"text",value:a,onChange:R=>u(R.target.value),onKeyDown:R=>{R.key==="Enter"&&_(),R.key==="Escape"&&T()},className:"w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1",autoFocus:!0,disabled:c}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:_,disabled:c,className:"fa-button-glass text-xs px-2 py-1",children:"Save"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:T,disabled:c,className:"fa-button-glass text-xs px-2 py-1",children:"Cancel"})]})]}):l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsx("h3",{className:`text-lg font-medium flex-1 ${p?"line-through text-fa-gray-500":"text-fa-gray-800"}`,children:e.title}),m&&l.jsxs("div",{className:"flex items-center text-fa-error ml-2",children:[l.jsx(xd,{className:"w-4 h-4 mr-1"}),l.jsx("span",{className:"fa-caption",children:"Overdue"})]})]}),e.description&&l.jsx("p",{className:"fa-body text-fa-gray-600 mt-1 line-clamp-2",children:e.description}),l.jsxs("div",{className:"flex items-center space-x-4 mt-2 flex-wrap gap-2",children:[l.jsx("span",{className:`fa-caption px-2 py-1 rounded-full bg-fa-white-glass ${E()}`,children:((A=e.status)==null?void 0:A.replace("_"," "))||"pending"}),S&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:S.color}}),l.jsx("span",{className:"fa-caption text-fa-gray-600",children:S.name})]}),e.tags&&e.tags.length>0&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(hr,{className:"w-3 h-3 text-fa-gray-400"}),e.tags.slice(0,3).map((R,D)=>l.jsx("span",{className:"fa-caption bg-fa-blue-100 text-fa-blue-700 px-2 py-1 rounded-full",children:R},D)),e.tags.length>3&&l.jsxs("span",{className:"fa-caption text-fa-gray-400",children:["+",e.tags.length-3]})]}),e.due_date&&l.jsxs("div",{className:`flex items-center ${m?"text-fa-error":"text-fa-gray-500"}`,children:[l.jsx(Ln,{className:"w-4 h-4 mr-1"}),l.jsx("span",{className:"fa-caption",children:Oe.formatDueDate(e.due_date)})]}),l.jsx(qo,{priority:e.priority,variant:"badge",size:"sm",showLabel:!1,animated:!0})]})]})}),!i&&l.jsxs(P.div,{className:"flex items-center space-x-1 ml-2",initial:{opacity:0,width:0},animate:{opacity:r?1:0,width:r?"auto":0},transition:{duration:.2},children:[l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:j,disabled:c,className:"p-1 text-fa-gray-400 hover:text-fa-blue-500 disabled:opacity-50",title:"Edit todo",children:l.jsx(tT,{className:"w-4 h-4"})}),l.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>f(!0),disabled:c,className:"p-1 text-fa-gray-400 hover:text-fa-error disabled:opacity-50",title:"Delete todo",children:l.jsx(lT,{className:"w-4 h-4"})})]})]}),Rm(e.priority)&&l.jsx("div",{className:"absolute top-0 left-0 w-full rounded-t-2xl",children:l.jsx(qo,{priority:e.priority,variant:"bar",animated:!0,className:"rounded-t-2xl"})}),co(e.priority)&&l.jsx("div",{className:"absolute -top-1 -left-1 -right-1 -bottom-1 rounded-2xl bg-gradient-to-r from-red-500/20 to-red-600/20 blur-sm -z-10 animate-pulse"})]}),l.jsx(mv,{isOpen:h,onClose:()=>f(!1),onConfirm:k,title:"Delete Todo",message:`Are you sure you want to delete "${e.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger",isLoading:c}),l.jsx(Ev,{isOpen:g,onClose:()=>v(!1),onSubmit:C,todo:e,mode:"edit"})]})};function xC(e,t){const{itemHeight:n,containerHeight:r,overscan:s=5,threshold:i=100}=t,[o,a]=w.useState(0),u=w.useRef(null),c=w.useMemo(()=>e.length*n,[e.length,n]),d=w.useMemo(()=>{const m=Math.max(0,Math.floor(o/n)-s),y=Math.ceil(r/n),k=Math.min(e.length-1,m+y+s*2);return{start:m,end:k}},[o,n,r,s,e.length]),h=w.useMemo(()=>{const m=[];for(let y=d.start;y<=d.end;y++)y>=0&&y<e.length&&m.push({index:y,item:e[y],offsetY:y*n,isVisible:!0});return m},[e,d,n]),f=w.useCallback(m=>{const y=m.currentTarget.scrollTop;a(y)},[]),g=w.useCallback((m,y="start")=>{if(!u.current||m<0||m>=e.length)return;let k;switch(y){case"start":k=m*n;break;case"center":k=m*n-(r-n)/2;break;case"end":k=m*n-r+n;break}k=Math.max(0,Math.min(k,c-r)),u.current.scrollTo({top:k,behavior:"smooth"})},[e.length,n,r,c]),v=w.useCallback(()=>{u.current&&u.current.scrollTo({top:0,behavior:"smooth"})},[]),x=w.useCallback(()=>{u.current&&u.current.scrollTo({top:c-r,behavior:"smooth"})},[c,r]);w.useEffect(()=>{if(!u.current)return;const m=o/Math.max(1,c-r),y=Math.max(0,c-r),k=m*y;Math.abs(k-o)>i&&(u.current.scrollTop=k,a(k))},[e.length,c,r,i]);const S=w.useMemo(()=>({style:{height:r,overflowY:"auto",overflowX:"hidden"},onScroll:f,ref:u}),[r,f]),p=w.useMemo(()=>({style:{height:c,position:"relative"}}),[c]);return{virtualItems:h,totalHeight:c,scrollToIndex:g,scrollToTop:v,scrollToBottom:x,containerProps:S,contentProps:p}}const $d=120,wC=5,Av=w.memo(({todo:e,index:t,offsetY:n,onUpdate:r,onDelete:s})=>l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.02,duration:.3},style:{position:"absolute",top:n,left:0,right:0,height:$d,paddingBottom:12},children:l.jsx(Pv,{todo:e,onUpdate:r,onDelete:s})}));Av.displayName="VirtualizedTodoItem";const Dv=({todos:e,onTodoUpdate:t,onTodoDelete:n,containerHeight:r,itemHeight:s=$d,overscan:i=wC,className:o="",emptyState:a,loadingState:u,isLoading:c=!1})=>{const d=e.length>50,h=xC(e,{itemHeight:s,containerHeight:r,overscan:i}),f=w.useCallback(v=>{t(v)},[t]),g=w.useCallback(v=>{n(v)},[n]);return c&&u?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:u}):e.length===0&&a?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:a}):e.length===0?l.jsx("div",{className:`flex items-center justify-center ${o}`,style:{height:r},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks found"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Try adjusting your filters or add a new task"})]})}):d?l.jsx("div",{className:o,children:l.jsx("div",{...h.containerProps,children:l.jsx("div",{...h.contentProps,children:h.virtualItems.map(({item:v,index:x,offsetY:S})=>l.jsx(Av,{todo:v,index:x,offsetY:S,onUpdate:f,onDelete:g},v.id))})})}):l.jsx("div",{className:`overflow-y-auto ${o}`,style:{height:r},children:l.jsx("div",{className:"space-y-3 p-1",children:e.map((v,x)=>l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:x*.02,duration:.3},children:l.jsx(Pv,{todo:v,onUpdate:f,onDelete:g})},v.id))})})},SC=w.memo(({onPerformanceMetrics:e,...t})=>{const n=performance.now();return Z.useEffect(()=>{const s=performance.now()-n;e==null||e({renderTime:s,itemCount:t.todos.length,visibleItems:Math.min(t.todos.length,Math.ceil(t.containerHeight/(t.itemHeight||$d)))})}),l.jsx(Dv,{...t})});SC.displayName="VirtualizedTodoListWithMetrics";const bC=({isVisible:e,selectedCount:t,totalCount:n,isAllSelected:r,isPartiallySelected:s,onSelectAll:i,onSelectNone:o,operations:a,isProcessing:u,onClose:c,className:d=""})=>{var k,j,C,_,T;const[h,f]=w.useState(!1),[g,v]=w.useState({isOpen:!1}),x=async E=>{f(!1),E.requiresConfirmation?v({isOpen:!0,operation:E}):await S(E)},S=async E=>{try{const A=await E.action([]);console.log("Bulk operation result:",A),A.success&&c()}catch(A){console.error("Bulk operation failed:",A)}},p=async()=>{g.operation&&await S(g.operation),v({isOpen:!1})},m=()=>r?l.jsx(oT,{className:"w-5 h-5 text-fa-blue-600"}):s?l.jsx(km,{className:"w-5 h-5 text-fa-blue-600 fill-current opacity-50"}):l.jsx(km,{className:"w-5 h-5 text-fa-gray-400"}),y=()=>{r?o():i()};return e?l.jsxs(l.Fragment,{children:[l.jsx(ae,{children:l.jsxs(P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted border border-fa-blue-200 shadow-lg ${d}`,children:[l.jsxs("div",{className:"flex items-center justify-between p-4",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:y,className:"flex items-center space-x-2 text-fa-gray-700 hover:text-fa-blue-600",disabled:u,children:[m(),l.jsx("span",{className:"text-sm font-medium",children:r?"Deselect All":"Select All"})]}),l.jsxs("div",{className:"text-sm text-fa-gray-600",children:[l.jsx("span",{className:"font-medium text-fa-blue-600",children:t})," ","of"," ",l.jsx("span",{className:"font-medium",children:n})," ","selected"]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"flex items-center space-x-1",children:a.slice(0,3).map(E=>l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>x(E),disabled:u||t===0,className:`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${E.variant==="danger"?"bg-fa-error text-white hover:bg-red-600 disabled:bg-fa-gray-300":E.variant==="warning"?"bg-fa-warning text-white hover:bg-yellow-600 disabled:bg-fa-gray-300":"fa-button-glass disabled:opacity-50"} disabled:cursor-not-allowed`,title:E.description,children:[l.jsx("span",{children:E.icon}),l.jsx("span",{className:"hidden sm:inline",children:E.name})]},E.id))}),a.length>3&&l.jsxs("div",{className:"relative",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f(!h),disabled:u||t===0,className:"fa-button-glass p-2 disabled:opacity-50 disabled:cursor-not-allowed",children:l.jsx(qk,{className:"w-4 h-4"})}),l.jsx(ae,{children:h&&l.jsx(P.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full right-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:l.jsx("div",{className:"p-2",children:a.slice(3).map(E=>l.jsxs(P.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>x(E),disabled:u,className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed ${E.variant==="danger"?"text-fa-error hover:bg-red-50":E.variant==="warning"?"text-fa-warning hover:bg-yellow-50":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[l.jsx("span",{className:"text-lg",children:E.icon}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium",children:E.name}),l.jsx("div",{className:"text-xs text-fa-gray-500",children:E.description})]})]},E.id))})})})]}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:u,children:l.jsx(wt,{className:"w-4 h-4"})})]})]}),u&&l.jsx(P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"border-t border-fa-gray-200 px-4 py-3",children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-4 h-4 border-2 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin"}),l.jsx("span",{className:"text-sm text-fa-gray-600",children:"Processing bulk operation..."})]})})]})}),l.jsx(mv,{isOpen:g.isOpen,onClose:()=>v({isOpen:!1}),onConfirm:p,title:`${(k=g.operation)==null?void 0:k.name}`,message:((j=g.operation)==null?void 0:j.confirmationMessage)||`Are you sure you want to ${(C=g.operation)==null?void 0:C.name.toLowerCase()} ${t} selected todo${t!==1?"s":""}?`,confirmText:((_=g.operation)==null?void 0:_.name)||"Confirm",cancelText:"Cancel",variant:((T=g.operation)==null?void 0:T.variant)||"info",isLoading:u})]}):null},Tl=[{id:"default",name:"Default",config:{field:"position",order:"asc"},icon:"📋",description:"Custom order"},{id:"priority_high",name:"Priority (High to Low)",config:{field:"priority",order:"desc"},icon:"🔥",description:"Most important first"},{id:"due_date_soon",name:"Due Date (Soonest)",config:{field:"due_date",order:"asc"},icon:"⏰",description:"Upcoming deadlines first"},{id:"alphabetical",name:"Alphabetical",config:{field:"title",order:"asc"},icon:"🔤",description:"A to Z"},{id:"recently_created",name:"Recently Created",config:{field:"created_at",order:"desc"},icon:"🆕",description:"Newest first"},{id:"recently_updated",name:"Recently Updated",config:{field:"updated_at",order:"desc"},icon:"📝",description:"Last modified first"}],Zm={very_high:5,high:4,medium:3,low:2,very_low:1},Jm={in_progress:5,pending:4,completed:3,archived:2,cancelled:1};function kC(e){const[t,n]=w.useState({field:"position",order:"asc"}),[r,s]=w.useState("default"),i=w.useMemo(()=>[...e].sort((v,x)=>{let S=0;switch(t.field){case"title":S=v.title.localeCompare(x.title);break;case"status":S=Jm[v.status]-Jm[x.status];break;case"priority":S=Zm[v.priority]-Zm[x.priority];break;case"due_date":!v.due_date&&!x.due_date?S=0:v.due_date?x.due_date?S=new Date(v.due_date).getTime()-new Date(x.due_date).getTime():S=-1:S=1;break;case"created_at":S=new Date(v.created_at).getTime()-new Date(x.created_at).getTime();break;case"updated_at":S=new Date(v.updated_at).getTime()-new Date(x.updated_at).getTime();break;case"position":S=v.position-x.position;break;case"estimated_duration":const p=v.estimated_duration?parseInt(v.estimated_duration):0,m=x.estimated_duration?parseInt(x.estimated_duration):0;S=p-m;break;default:S=0}return t.order==="desc"?-S:S}),[e,t]),o=w.useCallback((g,v)=>{n(x=>({field:g,order:v||(x.field===g&&x.order==="asc"?"desc":"asc")})),s("")},[]),a=w.useCallback(g=>{const v=Tl.find(x=>x.id===g);v&&(n(v.config),s(g))},[]),u=w.useCallback(()=>{n(g=>({...g,order:g.order==="asc"?"desc":"asc"})),s("")},[]),c=w.useCallback(g=>t.field!==g?null:t.order==="asc"?"↑":"↓",[t]),d=w.useCallback(g=>t.field===g,[t.field]),h=w.useMemo(()=>{const g=Tl.find(S=>S.id===r);if(g)return g.description||g.name;const v=t.field.replace("_"," "),x=t.order==="asc"?"ascending":"descending";return`${v} (${x})`},[t,r]),f=w.useCallback((g,v="asc")=>{console.log("Secondary sort not yet implemented:",g,v)},[]);return{sortConfig:t,sortedTodos:i,updateSort:o,applyPreset:a,toggleSortOrder:u,getSortIndicator:c,isSortedBy:d,sortDescription:h,presets:Tl,activePreset:r,addSecondarySort:f}}const jl={search:"",status:[],priority:[],tags:[],dueDate:"all",category:[],isCompleted:void 0},ep=[{id:"all",name:"All Tasks",filters:{},icon:"📋"},{id:"active",name:"Active",filters:{status:["pending","in_progress"]},icon:"⚡"},{id:"completed",name:"Completed",filters:{status:["completed"]},icon:"✅"},{id:"overdue",name:"Overdue",filters:{dueDate:"overdue"},icon:"🚨"},{id:"high_priority",name:"High Priority",filters:{priority:["high","very_high"]},icon:"🔥"},{id:"today",name:"Due Today",filters:{dueDate:"today"},icon:"📅"}];function TC(e,t=[]){const[n,r]=w.useState(jl),[s,i]=w.useState("all"),o=w.useMemo(()=>e.filter(f=>{var g;if(n.search){const v=n.search.toLowerCase();if(!(f.title.toLowerCase().includes(v)||((g=f.description)==null?void 0:g.toLowerCase().includes(v))||f.tags.some(S=>S.toLowerCase().includes(v))))return!1}if(n.status.length>0&&!n.status.includes(f.status)||n.priority.length>0&&!n.priority.includes(f.priority)||n.tags.length>0&&!n.tags.some(x=>f.tags.some(S=>S.toLowerCase().includes(x.toLowerCase())))||n.category.length>0&&(!f.category_id||!n.category.includes(f.category_id)))return!1;if(n.dueDate!=="all"){const v=new Date,x=new Date(v.getFullYear(),v.getMonth(),v.getDate()),S=new Date(x);S.setDate(S.getDate()+1);const p=new Date(x);p.setDate(p.getDate()+7);const m=new Date(x);switch(m.setMonth(m.getMonth()+1),n.dueDate){case"overdue":if(!f.due_date||new Date(f.due_date)>=x||f.status==="completed")return!1;break;case"today":if(!f.due_date)return!1;const y=new Date(f.due_date);if(y<x||y>=S)return!1;break;case"tomorrow":if(!f.due_date)return!1;const k=new Date(f.due_date);if(k<S||k>=new Date(S.getTime()+24*60*60*1e3))return!1;break;case"this_week":if(!f.due_date||new Date(f.due_date)>p)return!1;break;case"this_month":if(!f.due_date||new Date(f.due_date)>m)return!1;break;case"no_due_date":if(f.due_date)return!1;break}}if(n.isCompleted!==void 0){const v=f.status==="completed";if(n.isCompleted!==v)return!1}return!0}),[e,n]),a=w.useCallback((f,g)=>{r(v=>({...v,[f]:g})),i("")},[]),u=w.useCallback(f=>{const g=ep.find(v=>v.id===f);g&&(r(v=>({...jl,...v,...g.filters})),i(f))},[]),c=w.useCallback(()=>{r(jl),i("all")},[]),d=w.useMemo(()=>{const f=[];return n.search&&f.push(`Search: "${n.search}"`),n.status.length>0&&f.push(`Status: ${n.status.join(", ")}`),n.priority.length>0&&f.push(`Priority: ${n.priority.join(", ")}`),n.tags.length>0&&f.push(`Tags: ${n.tags.join(", ")}`),n.dueDate!=="all"&&f.push(`Due: ${n.dueDate.replace("_"," ")}`),n.category.length>0&&f.push(`Category: ${n.category.join(", ")}`),{activeFilters:f,hasActiveFilters:f.length>0,totalFiltered:o.length,totalOriginal:e.length}},[n,o.length,e.length]),h=w.useMemo(()=>{const f=[...new Set(e.map(x=>x.status))],g=[...new Set(e.map(x=>x.priority))],v=[...new Set(e.flatMap(x=>x.tags))];return{statuses:f,priorities:g,tags:v,categories:t}},[e,t]);return{filters:n,filteredTodos:o,updateFilter:a,applyPreset:u,clearFilters:c,filterSummary:d,filterOptions:h,presets:ep,activePreset:s}}function jC(e,t,n){const[r,s]=w.useState(new Set),[i,o]=w.useState(!1),[a,u]=w.useState(null),c=w.useMemo(()=>e.filter(C=>r.has(C.id)),[e,r]),d=w.useCallback(C=>{s(_=>new Set([..._,C]))},[]),h=w.useCallback(C=>{s(_=>{const T=new Set(_);return T.delete(C),T})},[]),f=w.useCallback(C=>{s(_=>{const T=new Set(_);return T.has(C)?T.delete(C):T.add(C),T})},[]),g=w.useCallback(()=>{s(new Set(e.map(C=>C.id)))},[e]),v=w.useCallback(()=>{s(new Set)},[]),x=w.useCallback(C=>{const _=e.filter(T=>T.status===C).map(T=>T.id);s(new Set(_))},[e]),S=w.useCallback(C=>{const _=e.filter(T=>T.priority===C).map(T=>T.id);s(new Set(_))},[e]),p=w.useCallback(async C=>{const _=Array.from(r),T={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u(`update_status_${C}`);try{const E=_.map(async D=>{try{return{success:!0,todo:await Oe.updateTodoStatus(D,C)}}catch(F){return{success:!1,error:F instanceof Error?F.message:"Unknown error",todoId:D}}}),A=await Promise.all(E),R=[];A.forEach(D=>{D.success&&"todo"in D?(R.push(D.todo),D.processedCount++):!D.success&&"error"in D&&(D.failedCount++,D.errors.push(`Failed to update todo ${D.todoId}: ${D.error}`))}),t(R),s(new Set),T.processedCount=R.length,T.failedCount=A.length-R.length,T.success=T.failedCount===0}catch(E){T.success=!1,T.errors.push(E instanceof Error?E.message:"Unknown error")}finally{o(!1)}return T},[r,t]),m=w.useCallback(async C=>{const _=Array.from(r),T={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u(`update_priority_${C}`);try{const E=_.map(async D=>{try{return{success:!0,todo:await Oe.updateTodo(D,{priority:C})}}catch(F){return{success:!1,error:F instanceof Error?F.message:"Unknown error",todoId:D}}}),A=await Promise.all(E),R=[];A.forEach(D=>{D.success&&"todo"in D?(R.push(D.todo),D.processedCount++):!D.success&&"error"in D&&(D.failedCount++,D.errors.push(`Failed to update todo ${D.todoId}: ${D.error}`))}),t(R),s(new Set),T.processedCount=R.length,T.failedCount=A.length-R.length,T.success=T.failedCount===0}catch(E){T.success=!1,T.errors.push(E instanceof Error?E.message:"Unknown error")}finally{o(!1)}return T},[r,t]),y=w.useCallback(async()=>{const C=Array.from(r),_={success:!0,processedCount:0,failedCount:0,errors:[]};o(!0),u("delete");try{const T=C.map(async A=>{try{return await Oe.deleteTodo(A),{success:!0,todoId:A}}catch(R){return{success:!1,error:R instanceof Error?R.message:"Unknown error",todoId:A}}}),E=await Promise.all(T);E.forEach(A=>{A.success?(n(A.todoId),A.processedCount++):(A.failedCount++,A.errors.push(`Failed to delete todo ${A.todoId}: ${A.error}`))}),s(new Set),_.processedCount=E.filter(A=>A.success).length,_.failedCount=E.filter(A=>!A.success).length,_.success=_.failedCount===0}catch(T){_.success=!1,_.errors.push(T instanceof Error?T.message:"Unknown error")}finally{o(!1)}return _},[r,n]),k=w.useMemo(()=>[{id:"mark_completed",name:"Mark as Completed",icon:"✅",description:"Mark selected todos as completed",action:()=>p("completed")},{id:"mark_pending",name:"Mark as Pending",icon:"⏳",description:"Mark selected todos as pending",action:()=>p("pending")},{id:"mark_in_progress",name:"Mark as In Progress",icon:"🔄",description:"Mark selected todos as in progress",action:()=>p("in_progress")},{id:"set_high_priority",name:"Set High Priority",icon:"🔥",description:"Set selected todos to high priority",action:()=>m("high")},{id:"set_medium_priority",name:"Set Medium Priority",icon:"⚡",description:"Set selected todos to medium priority",action:()=>m("medium")},{id:"set_low_priority",name:"Set Low Priority",icon:"📝",description:"Set selected todos to low priority",action:()=>m("low")},{id:"delete",name:"Delete",icon:"🗑️",description:"Delete selected todos permanently",action:y,requiresConfirmation:!0,confirmationMessage:"Are you sure you want to delete the selected todos? This action cannot be undone.",variant:"danger"}],[p,m,y]),j=w.useMemo(()=>({selectedCount:r.size,totalCount:e.length,hasSelection:r.size>0,isAllSelected:r.size===e.length&&e.length>0,isPartiallySelected:r.size>0&&r.size<e.length}),[r.size,e.length]);return{selectedTodoIds:r,selectedTodos:c,selectionState:j,selectTodo:d,deselectTodo:h,toggleTodoSelection:f,selectAll:g,selectNone:v,selectByStatus:x,selectByPriority:S,availableOperations:k,bulkUpdateStatus:p,bulkUpdatePriority:m,bulkDelete:y,isProcessing:i,lastOperation:a}}const CC=({containerHeight:e=600,className:t=""})=>{const{todos:n,categories:r,isLoading:s,error:i,filters:o,selectedTodos:a,loadTodos:u,loadCategories:c,updateTodo:d,deleteTodo:h,setSearchQuery:f,setFilters:g,selectTodo:v,deselectTodo:x,clearSelection:S}=Md(),{todoViewMode:p,setTodoViewMode:m}=xj(),[y,k]=w.useState(!1),[j,C]=w.useState(new Date),_=Sj(o.searchQuery,300),T=w.useCallback(async U=>{try{await d(U.id,U)}catch(L){console.error("Failed to update todo:",L)}},[d]),E=w.useCallback(async U=>{try{await h(U)}catch(L){console.error("Failed to delete todo:",L)}},[h]),A=w.useCallback(async U=>{try{for(const L of U)await d(L.id,L)}catch(L){console.error("Failed to bulk update todos:",L)}},[d]),R=TC(n,r),D=kC(R.filteredTodos),F=jC(n,A,E);w.useEffect(()=>{u(),c()},[u,c]),w.useEffect(()=>{f(_)},[_,f]);const Y=w.useCallback(async()=>{try{await u(),C(new Date)}catch(U){console.error("Failed to refresh todos:",U)}},[u]),G=w.useMemo(()=>{const U=new Set;return n.forEach(L=>{L.title.split(" ").forEach(W=>{W.length>2&&U.add(W)}),L.tags.forEach(W=>U.add(W))}),Array.from(U).slice(0,10)},[n]),H=w.useCallback(U=>{m(U)},[m]);return s?l.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-8 h-8 border-4 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin mx-auto mb-4"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Loading todos..."})]})}):i?l.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"⚠️"}),l.jsx("h3",{className:"fa-heading-3 mb-2 text-fa-error",children:"Error Loading Todos"}),l.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:i}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:u,className:"fa-button-primary px-4 py-2",children:"Try Again"})]})}):l.jsxs("div",{className:`w-full h-full flex flex-col ${t}`,children:[l.jsxs("div",{className:"flex-shrink-0 space-y-4 mb-6",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("div",{className:"flex-1",children:l.jsx(kj,{value:o.searchQuery,onChange:f,onFilterToggle:()=>k(!y),suggestions:G,isFilterActive:Object.values(o).some(U=>Array.isArray(U)?U.length>0:typeof U=="string"?U!=="":typeof U=="object"&&U!==null?Object.keys(U).length>0:!1)})}),l.jsx("div",{className:"flex items-center space-x-1 fa-glass-panel px-2 py-1 rounded-lg",children:["list","grid","kanban"].map(U=>l.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>H(U),className:`p-2 rounded-lg transition-all duration-200 ${p===U?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,title:`${U.charAt(0).toUpperCase()+U.slice(1)} view`,children:[U==="list"&&l.jsx(Yy,{className:"w-4 h-4"}),U==="grid"&&l.jsx(Xk,{className:"w-4 h-4"}),U==="kanban"&&l.jsx(Kk,{className:"w-4 h-4"})]},U))}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:Y,disabled:s,className:"fa-button-glass p-2 disabled:opacity-50",title:"Refresh todos",children:l.jsx(Jy,{className:`w-4 h-4 ${s?"animate-spin":""}`})})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx(Pj,{sortConfig:D.sortConfig,onUpdateSort:D.updateSort,onApplyPreset:D.applyPreset,onToggleSortOrder:D.toggleSortOrder,presets:D.presets,activePreset:D.activePreset,sortDescription:D.sortDescription}),l.jsx("div",{className:"text-sm text-fa-gray-500",children:R.filterSummary.hasActiveFilters?l.jsxs("span",{children:[R.filterSummary.totalFiltered," of ",R.filterSummary.totalOriginal," tasks"]}):l.jsxs("span",{children:[n.length," tasks"]})})]})]}),l.jsx(ae,{children:F.selectionState.hasSelection&&l.jsx("div",{className:"flex-shrink-0 mb-4",children:l.jsx(bC,{isVisible:F.selectionState.hasSelection,selectedCount:F.selectionState.selectedCount,totalCount:F.selectionState.totalCount,isAllSelected:F.selectionState.isAllSelected,isPartiallySelected:F.selectionState.isPartiallySelected,onSelectAll:F.selectAll,onSelectNone:F.selectNone,operations:F.availableOperations,isProcessing:F.isProcessing,onClose:F.selectNone})})}),l.jsx("div",{className:"flex-1 min-h-0",children:l.jsx(Dv,{todos:D.sortedTodos,onTodoUpdate:T,onTodoDelete:E,containerHeight:e-200,isLoading:s,emptyState:R.filterSummary.hasActiveFilters?l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🔍"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No matching tasks"}),l.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:"Try adjusting your filters or search terms"}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:R.clearFilters,className:"fa-button-glass px-4 py-2",children:"Clear Filters"})]}):l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),l.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks yet"}),l.jsx("p",{className:"fa-body text-fa-gray-500",children:"Add your first task to get started!"})]})})}),l.jsx(Ej,{isOpen:y,onClose:()=>k(!1),filters:R.filters,onUpdateFilter:R.updateFilter,onApplyPreset:R.applyPreset,onClearFilters:R.clearFilters,presets:R.presets,activePreset:R.activePreset,filterOptions:R.filterOptions,filterSummary:R.filterSummary})]})},NC=({onTodoCreate:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(!1),[i,o]=w.useState(!1),a=d=>{d.preventDefault(),t.trim()&&o(!0)},u=d=>{e==null||e(d),n(""),o(!1)},c=()=>{o(!0)};return l.jsxs(l.Fragment,{children:[l.jsx("form",{onSubmit:a,className:"w-full",children:l.jsx("div",{className:`fa-glass-panel transition-all duration-300 ${r?"ring-2 ring-fa-blue-400 shadow-lg":"shadow-md"}`,children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-4 text-fa-blue-500 hover:text-fa-blue-600",children:l.jsx(Sa,{className:"w-5 h-5"})}),l.jsx("input",{type:"text",value:t,onChange:d=>n(d.target.value),onFocus:()=>s(!0),onBlur:()=>s(!1),onClick:c,placeholder:"What needs to be done?",className:"flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none cursor-pointer",readOnly:!0}),l.jsxs("div",{className:"flex items-center space-x-2 pr-4",children:[l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(Ln,{className:"w-4 h-4"})}),l.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:c,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:l.jsx(hr,{className:"w-4 h-4"})})]})]})})}),l.jsx(Ev,{isOpen:i,onClose:()=>o(!1),onSubmit:u,mode:"create"})]})},_C=({systemInfo:e})=>{const{theme:t,toggleTheme:n}=mT(),[r,s]=w.useState(!0);return l.jsxs("div",{className:"w-full h-full flex flex-col bg-transparent",children:[l.jsx(yT,{systemInfo:e,onToggleTheme:n,theme:t}),l.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[l.jsx(P.div,{className:`h-full ${r?"w-64":"w-0"}`,animate:{width:r?256:0},transition:{duration:.3,ease:[.4,0,.2,1]},children:l.jsx(vT,{})}),l.jsx("div",{className:"flex-1 flex flex-col overflow-hidden p-6",children:l.jsxs("div",{className:"fa-glass-panel-frosted flex-1 flex flex-col rounded-2xl p-6",children:[l.jsxs("div",{className:"mb-6",children:[l.jsx("h1",{className:"fa-heading-1 mb-2",children:"My Tasks"}),l.jsx("p",{className:"fa-body text-fa-gray-600",children:"Stay organized and productive"})]}),l.jsx("div",{className:"mb-6",children:l.jsx(NC,{})}),l.jsx("div",{className:"flex-1 overflow-hidden",children:l.jsx(CC,{})})]})})]})]})},EC=()=>l.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-fa-blue-50 to-fa-aqua-50",children:l.jsxs("div",{className:"text-center",children:[l.jsx(P.div,{className:"w-16 h-16 mx-auto mb-6 rounded-full border-4 border-fa-blue-500 border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),l.jsx(P.h1,{className:"fa-heading-1 mb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Modern Todo"}),l.jsx(P.p,{className:"fa-caption text-fa-gray-600",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Loading your productivity experience..."})]})});class PC extends w.Component{constructor(){super(...arguments);Ut(this,"state",{hasError:!1,error:void 0})}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Uncaught error:",n,r)}render(){var n;return this.state.hasError?l.jsx("div",{className:"w-full h-full flex items-center justify-center p-6",children:l.jsxs(P.div,{className:"fa-glass-panel-frosted p-8 max-w-md w-full text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[l.jsx("div",{className:"text-fa-error text-5xl mb-4",children:"⚠️"}),l.jsx("h2",{className:"fa-heading-2 mb-4",children:"Something went wrong"}),l.jsx("p",{className:"fa-body text-fa-gray-600 mb-6",children:((n=this.state.error)==null?void 0:n.message)||"An unexpected error occurred."}),l.jsx("button",{className:"fa-button-primary px-6 py-3 rounded-lg font-medium",onClick:()=>window.location.reload(),children:"Reload Application"})]})}):this.props.children}}const AC=({onSwitchToRegister:e,onSwitchToReset:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(""),[o,a]=w.useState(!1),[u,c]=w.useState(!1),[d,h]=w.useState(null),{login:f}=ss(),g=async v=>{v.preventDefault(),c(!0),h(null);try{await f(n,s)}catch(x){h(x instanceof Error?x.message:"Login failed")}finally{c(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Pn,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Welcome Back"}),l.jsx("p",{className:"fa-body text-gray-600",children:"Sign in to your account"})]}),d&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:d}),l.jsxs(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm",children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("div",{className:"font-medium",children:"🧪 Development Testing Account"}),l.jsx("button",{type:"button",onClick:()=>{r("GOD"),i("123456")},className:"text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 transition-colors",children:"Quick Fill"})]}),l.jsxs("div",{className:"text-xs",children:["Username: ",l.jsx("span",{className:"font-mono font-semibold",children:"GOD"}),l.jsx("br",{}),"Password: ",l.jsx("span",{className:"font-mono font-semibold",children:"123456"})]})]}),l.jsxs("form",{onSubmit:g,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:n,onChange:v=>r(v.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"password",type:o?"text":"password",value:s,onChange:v=>i(v.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter your password",required:!0}),l.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:o?l.jsx(li,{className:"h-5 w-5"}):l.jsx(ui,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),l.jsx("div",{className:"text-sm",children:l.jsx("button",{type:"button",onClick:t,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot password?"})})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:u,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:u?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",l.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up"})]})})]})})},DC=({onSwitchToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,o]=w.useState(""),[a,u]=w.useState(""),[c,d]=w.useState(!1),[h,f]=w.useState(!1),[g,v]=w.useState(!1),[x,S]=w.useState(null),{register:p}=ss(),m=async y=>{if(y.preventDefault(),i!==a){S("Passwords do not match");return}v(!0),S(null);try{await p(t,i,r)}catch(k){S(k instanceof Error?k.message:"Registration failed")}finally{v(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Pn,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Create Account"}),l.jsx("p",{className:"fa-body text-gray-600",children:"Join our todo application"})]}),x&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),l.jsxs("form",{onSubmit:m,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:t,onChange:y=>n(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Choose a username",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(bd,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"email",type:"email",value:r,onChange:y=>s(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your email"})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"password",type:c?"text":"password",value:i,onChange:y=>o(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Create a strong password",required:!0}),l.jsx("button",{type:"button",onClick:()=>d(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:c?l.jsx(li,{className:"h-5 w-5"}):l.jsx(ui,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"confirmPassword",type:h?"text":"password",value:a,onChange:y=>u(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm your password",required:!0}),l.jsx("button",{type:"button",onClick:()=>f(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:h?l.jsx(li,{className:"h-5 w-5"}):l.jsx(ui,{className:"h-5 w-5"})})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",l.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})})},MC=({onBackToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,o]=w.useState(""),[a,u]=w.useState(!1),[c,d]=w.useState(!1),[h,f]=w.useState("request"),[g,v]=w.useState(!1),[x,S]=w.useState(null),[p,m]=w.useState(null),y=async j=>{j.preventDefault(),v(!0),S(null),m(null);try{await new Promise(C=>setTimeout(C,1e3)),f("reset"),m("Reset instructions sent to your email")}catch{S("Failed to send reset instructions")}finally{v(!1)}},k=async j=>{if(j.preventDefault(),r!==i){S("Passwords do not match");return}if(r.length<12){S("Password must be at least 12 characters long");return}v(!0),S(null),m(null);try{await new Promise(C=>setTimeout(C,1e3)),m("Password reset successfully")}catch{S("Failed to reset password")}finally{v(!1)}};return l.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:l.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx(P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(tr,{className:"w-8 h-8 text-white"})}),l.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:h==="request"?"Reset Password":"Set New Password"}),l.jsx("p",{className:"fa-body text-gray-600",children:h==="request"?"Enter your username to receive reset instructions":"Enter your new password"})]}),x&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),p&&l.jsx(P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm",children:p}),h==="request"?l.jsxs("form",{onSubmit:y,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Pn,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"username",type:"text",value:t,onChange:j=>n(j.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Sending instructions..."]}):"Send Reset Instructions"})]}):l.jsxs("form",{onSubmit:k,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"newPassword",type:a?"text":"password",value:r,onChange:j=>s(j.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter new password",required:!0}),l.jsx("button",{type:"button",onClick:()=>u(!a),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:a?l.jsx(li,{className:"h-5 w-5"}):l.jsx(ui,{className:"h-5 w-5"})})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{id:"confirmPassword",type:c?"text":"password",value:i,onChange:j=>o(j.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm new password",required:!0}),l.jsx("button",{type:"button",onClick:()=>d(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:c?l.jsx(li,{className:"h-5 w-5"}):l.jsx(ui,{className:"h-5 w-5"})})]})]}),l.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Resetting password..."]}):"Reset Password"})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("button",{onClick:e,className:"flex items-center justify-center mx-auto text-sm text-blue-600 hover:text-blue-500",children:[l.jsx(Hk,{className:"w-4 h-4 mr-1"}),"Back to login"]})})]})})},RC=()=>{const[e,t]=w.useState("login"),n=()=>{t("register")},r=()=>{t("login")},s=()=>{t("reset")},i=()=>{t("login")};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100 p-4 sm:p-6 lg:p-8",children:l.jsx("div",{className:"w-full max-w-md mx-auto",children:l.jsx(ae,{mode:"wait",children:l.jsxs(P.div,{initial:{opacity:0,x:e==="login"?-20:e==="register"?0:20},animate:{opacity:1,x:0},exit:{opacity:0,x:e==="login"?20:e==="register"?0:-20},transition:{duration:.3},className:"w-full",children:[e==="login"&&l.jsx(AC,{onSwitchToRegister:n,onSwitchToReset:s}),e==="register"&&l.jsx(DC,{onSwitchToLogin:r}),e==="reset"&&l.jsx(MC,{onBackToLogin:i})]},e)})})})},LC=({children:e})=>{const{isAuthenticated:t,loading:n}=ss();return n?l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100",children:l.jsxs(P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?l.jsx(l.Fragment,{children:e}):l.jsx(RC,{})},VC=()=>{const[e,t]=w.useState(!0),[n,r]=w.useState(null),s=_v();return w.useEffect(()=>{(async()=>{try{if(wj(),window.electronAPI){const o=await window.electronAPI.system.getInfo();r(o)}await new Promise(o=>setTimeout(o,1500)),t(!1)}catch(o){console.error("Failed to initialize app:",o),s.error("Initialization Failed","Failed to initialize the application. Please try restarting."),t(!1)}})()},[]),e?l.jsx(EC,{}):l.jsxs(PC,{children:[l.jsx(cT,{children:l.jsx(hT,{children:l.jsx(pT,{children:l.jsx(LC,{children:l.jsx(ae,{mode:"wait",children:l.jsx(P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,ease:[.4,0,.2,1]},className:"w-full h-full",children:l.jsx(_C,{systemInfo:n})},"main-app")})})})})}),l.jsx(uC,{toasts:s.toasts,onDismiss:s.dismissToast,position:"top-right"})]})},Mv=document.getElementById("root");if(!Mv)throw new Error("Root element not found");Cl.createRoot(Mv).render(l.jsx(Z.StrictMode,{children:l.jsx(VC,{})}));
