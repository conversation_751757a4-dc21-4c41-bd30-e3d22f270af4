<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Modern Todo - Frutiger Aero Design</title>
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Inter+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    /* Prevent FOUC and set initial glassmorphism background */
    body {
      margin: 0;
      padding: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1) 0%,
        rgba(126, 211, 33, 0.05) 50%,
        rgba(135, 206, 235, 0.1) 100%);
      background-attachment: fixed;
      min-height: 100vh;
      overflow: hidden;
    }
    
    #root {
      width: 100vw;
      height: 100vh;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }
    
    /* Loading spinner */
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
    }
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="./assets/index-DhFbE4Y4.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/index-BPI8L9kE.css">
</head>
<body>
  <div id="root">
    <div class="loading-container">
      <div class="loading-spinner"></div>
    </div>
  </div>
</body>
</html>