// Export all stores
export { useTodoStore } from './todoStore';
export type { TodoState, TodoFilters } from './todoStore';

export { useAuthStore, useSessionValidation } from './authStore';
export type { AuthState, RegisterData } from './authStore';

export { useUIStore, useNotifications } from './uiStore';
export type { 
  UIState, 
  Theme, 
  ViewMode, 
  CurrentView, 
  ModalType, 
  Notification, 
  NotificationAction 
} from './uiStore';

// Store initialization helper
export const initializeStores = () => {
  try {
    // Initialize theme from localStorage using the store's built-in initialization
    // Access the store actions directly, not through getState()
    const initializeTheme = useUIStore.getState().initializeTheme;
    if (initializeTheme) {
      initializeTheme();
    } else {
      // Fallback theme initialization
      const savedTheme = localStorage.getItem('fa-theme');
      if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
      } else {
        document.documentElement.setAttribute('data-theme', 'light');
      }
    }

    // You can add other store initialization logic here
    console.log('Stores initialized successfully');
  } catch (error) {
    console.error('Error initializing stores:', error);
    // Continue execution even if initialization fails
  }
};

// Combined store hooks for common use cases
export const useAppState = () => {
  const todoStore = useTodoStore();
  const authStore = useAuthStore();
  const uiStore = useUIStore();
  
  return {
    todos: todoStore,
    auth: authStore,
    ui: uiStore,
  };
};

// Selector hooks for performance optimization
export const useTodoCount = () => useTodoStore(state => state.totalCount);
export const useCompletedCount = () => useTodoStore(state => state.completedCount);
export const usePendingCount = () => useTodoStore(state => state.pendingCount);
export const useFilteredTodos = () => useTodoStore(state => state.getFilteredTodos());
export const useTodoStats = () => useTodoStore(state => state.getTodoStats());

export const useCurrentUser = () => useAuthStore(state => state.user);
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated);
export const useUserDisplayName = () => useAuthStore(state => state.getUserDisplayName());

export const useCurrentTheme = () => useUIStore(state => state.theme);
export const useCurrentView = () => useUIStore(state => state.currentView);
export const useActiveModal = () => useUIStore(state => state.activeModal);
export const useGlobalLoading = () => useUIStore(state => state.globalLoading);
